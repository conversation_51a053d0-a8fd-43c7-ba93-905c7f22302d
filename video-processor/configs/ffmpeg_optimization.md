# FFMPEG优化配置说明

## 当前优化配置

### 1. 编码器优化
- **编码器**: libx264
- **预设**: veryfast (比fast更快，质量略有下降但可接受)
- **CRF**: 21 (恒定质量模式，21是高质量设置)
- **线程数**: 8 (匹配CPU核心数)

### 2. 音频处理
- **音频编码**: copy (直接复制，不重新编码，节省时间)
- **音频码率**: 128k (仅在需要重新编码时使用)

### 3. 优化参数
- **movflags**: +faststart (优化流媒体播放)
- **tune**: film (针对电影内容优化)
- **x264-params**: 高级优化参数
  - ref=3: 参考帧数量
  - bframes=3: B帧数量
  - b-adapt=1: 自适应B帧
  - direct=auto: 直接预测模式
  - me=umh: 运动估计算法
  - subme=6: 子像素运动估计
  - analyse=all: 分析所有分区类型

### 4. HLS切片优化
- **独立段**: independent_segments
- **缓冲区**: 动态计算 (码率 × 2)
- **最大码率**: 与目标码率相同

## 性能基准

### AMD EPYC 9634 84-Core (8核配置)
- **预期处理速度**: 1-3x实时速度
- **1080p视频**: 约30-60分钟/小时内容
- **720p视频**: 约15-30分钟/小时内容
- **480p视频**: 约10-20分钟/小时内容

### 瓶颈分析
1. **CPU**: 8核应该足够，但可能受单线程性能限制
2. **内存**: 16GB足够处理大多数视频
3. **存储**: 可能是主要瓶颈，特别是临时文件I/O
4. **网络**: 上传到Cloudflare R2的带宽

## 进一步优化建议

### 1. 硬件加速
```bash
# 检查是否支持硬件加速
ffmpeg -hwaccels

# 如果支持VAAPI (Intel/AMD)
-hwaccel vaapi -hwaccel_device /dev/dri/renderD128

# 如果支持NVENC (NVIDIA)
-c:v h264_nvenc
```

### 2. 存储优化
- 使用SSD存储临时文件
- 增加临时目录到内存文件系统 (tmpfs)
- 分离输入、处理、输出目录到不同磁盘

### 3. 网络优化
- 并行上传多个文件
- 使用分片上传
- 压缩传输

### 4. 处理流程优化
- 预处理：快速检查视频格式和编码
- 智能跳过：如果已经是目标格式则跳过转码
- 分段处理：大文件分段处理
- 缓存：缓存常用的处理结果

## 监控指标

### 关键指标
1. **处理速度**: fps (frames per second)
2. **CPU使用率**: 应该接近100%
3. **内存使用**: 监控是否有内存泄漏
4. **磁盘I/O**: 读写速度和IOPS
5. **网络带宽**: 上传速度

### 告警阈值
- 处理速度 < 0.5x 实时速度
- CPU使用率 < 70% (可能有瓶颈)
- 内存使用 > 80%
- 磁盘使用 > 90%
- 临时文件目录 > 50GB

## 故障排除

### 常见问题
1. **处理速度慢**
   - 检查CPU使用率
   - 检查磁盘I/O
   - 检查内存使用
   - 检查网络连接

2. **内存不足**
   - 减少并发处理数量
   - 增加交换空间
   - 优化FFMPEG参数

3. **磁盘空间不足**
   - 清理临时文件
   - 增加磁盘空间
   - 优化文件管理

4. **网络上传慢**
   - 检查带宽
   - 优化上传参数
   - 使用CDN加速
