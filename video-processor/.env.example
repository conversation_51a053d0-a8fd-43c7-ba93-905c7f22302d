# Go视频处理服务器配置

# 服务器配置
PORT=8080
GIN_MODE=release
LOG_LEVEL=info

# Node.js API服务器配置
API_SERVER_URL=http://localhost:4000
API_SERVER_TOKEN=your-super-secure-jwt-token-here

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# 文件存储配置
TEMP_DIR=/tmp/javflix-processing
MAX_DISK_USAGE=80
CLEANUP_ENABLED=true

# 并发配置 - 优化为8核CPU
MAX_CONCURRENT_DOWNLOADS=6
MAX_CONCURRENT_PROCESSES=4
MAX_CONCURRENT_UPLOADS=8
WORKER_POOL_SIZE=12

# CDN配置 (cloudflare_r2, aliyun_oss, aws_s3)
CDN_PROVIDER=cloudflare_r2

# Cloudflare R2配置
R2_ENDPOINT=https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=6f2ada83e825a45f0ab31fb0152860f8
R2_SECRET_ACCESS_KEY=8100c3f1be89c482f27bfaf6c3093682
R2_BUCKET=javflix-videos
R2_PUBLIC_DOMAIN=https://cdn.javflix.tv

# 阿里云OSS配置 (备选)
ALIYUN_OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
ALIYUN_OSS_ACCESS_KEY=your-oss-access-key
ALIYUN_OSS_SECRET_KEY=your-oss-secret-key
ALIYUN_OSS_BUCKET=javflix-videos

# AWS S3配置 (备选)
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=javflix-videos

# 安全配置
ALLOWED_IPS=127.0.0.1,::1
JWT_SECRET=your-jwt-secret-must-match-nodejs-api
HMAC_SECRET=your-hmac-secret

# 监控配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=30s