#!/bin/bash

# FFMPEG性能监控脚本
# 用于监控视频处理性能和资源使用情况

LOG_FILE="/tmp/ffmpeg_monitor.log"
INTERVAL=5  # 监控间隔（秒）

echo "=== FFMPEG性能监控开始 $(date) ===" >> $LOG_FILE

while true; do
    echo "--- $(date) ---" >> $LOG_FILE
    
    # 检查FFMPEG进程
    FFMPEG_PROCS=$(ps aux | grep ffmpeg | grep -v grep | wc -l)
    echo "当前FFMPEG进程数: $FFMPEG_PROCS" >> $LOG_FILE
    
    if [ $FFMPEG_PROCS -gt 0 ]; then
        # 显示FFMPEG进程详情
        echo "FFMPEG进程详情:" >> $LOG_FILE
        ps aux | grep ffmpeg | grep -v grep | awk '{print $2, $3, $4, $11}' >> $LOG_FILE
        
        # CPU和内存使用情况
        echo "系统资源使用:" >> $LOG_FILE
        top -bn1 | head -5 >> $LOG_FILE
        
        # 磁盘I/O
        echo "磁盘使用:" >> $LOG_FILE
        df -h | grep -E "(Filesystem|/dev/)" >> $LOG_FILE
        
        # 临时文件目录大小
        if [ -d "/tmp/javflix-processing" ]; then
            TEMP_SIZE=$(du -sh /tmp/javflix-processing 2>/dev/null | cut -f1)
            echo "临时文件目录大小: $TEMP_SIZE" >> $LOG_FILE
        fi
        
        # 网络连接（检查上传进度）
        UPLOAD_CONNS=$(netstat -an | grep :443 | grep ESTABLISHED | wc -l)
        echo "活跃上传连接数: $UPLOAD_CONNS" >> $LOG_FILE
    else
        echo "没有运行中的FFMPEG进程" >> $LOG_FILE
    fi
    
    echo "" >> $LOG_FILE
    sleep $INTERVAL
done
