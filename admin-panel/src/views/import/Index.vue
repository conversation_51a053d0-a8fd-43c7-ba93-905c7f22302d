<template>
  <div class="import-container">
    <div class="page-header">
      <h2>数据导入</h2>
    </div>

    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>JavBus数据源</span>
        </div>
      </template>

      <el-form :model="importForm" label-width="120px">
        <el-form-item label="采集页数">
          <el-input-number v-model="importForm.page" :min="1" :max="100" />
          <span class="form-tip">（从第1页开始采集到第N页，如设置4则采集1-4页）</span>
        </el-form-item>
        <el-form-item label="每页数量">
          <el-input-number v-model="importForm.count" :min="1" :max="50" />
          <span class="form-tip">（每页最多采集数量，建议不超过20个）</span>
        </el-form-item>
        <el-form-item label="磁力链接">
          <el-radio-group v-model="importForm.magnet">
            <el-radio value="exist">仅包含磁力链接</el-radio>
            <el-radio value="all">全部影片</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="影片类型">
          <el-radio-group v-model="importForm.type">
            <el-radio value="normal">一般影片</el-radio>
            <el-radio value="actress">女优影片</el-radio>
            <el-radio value="uncensored">无码影片</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleImport" :loading="importing">
            开始导入
          </el-button>
          <el-button @click="handleSearch">预览影片列表</el-button>
          <el-button type="warning" @click="handleUpdateReleaseDate" :loading="updatingReleaseDate">
            更新发行日期
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 自定义磁力下载 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>自定义磁力下载</span>
        </div>
      </template>

      <el-form :model="magnetForm" label-width="120px" :rules="magnetRules" ref="magnetFormRef">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="磁力链接" prop="magnetLink">
              <el-input
                v-model="magnetForm.magnetLink"
                placeholder="magnet:?xt=urn:btih:..."
                type="textarea"
                :rows="3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="影片标题" prop="title">
              <el-input v-model="magnetForm.title" placeholder="请输入影片标题" />
            </el-form-item>
            <el-form-item label="影片ID">
              <el-input v-model="magnetForm.movieId" placeholder="可选，如 SSIS-001" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级">
              <el-select v-model="magnetForm.priority" placeholder="选择优先级">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自动开始">
              <el-switch v-model="magnetForm.autoStart" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input v-model="magnetForm.remark" placeholder="可选备注信息" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleAddMagnetDownload" :loading="addingMagnet">
            添加下载任务
          </el-button>
          <el-button @click="resetMagnetForm">重置</el-button>
          <el-button type="info" @click="refreshMagnetTasks">
            <el-icon><Refresh /></el-icon>
            刷新任务列表
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 磁力下载任务列表 -->
    <el-card v-if="magnetTasks.length > 0" shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>磁力下载任务</span>
          <div>
            <el-tag type="info">总计: {{ magnetTasks.length }}</el-tag>
            <el-tag type="success" style="margin-left: 10px;">
              完成: {{ magnetTasks.filter(t => t.status === 'completed').length }}
            </el-tag>
            <el-tag type="warning" style="margin-left: 10px;">
              进行中: {{ magnetTasks.filter(t => ['downloading', 'processing', 'uploading'].includes(t.status)).length }}
            </el-tag>
          </div>
        </div>
      </template>

      <el-table :data="magnetTasks" v-loading="loadingMagnetTasks" style="width: 100%">
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="movieId" label="影片ID" width="120" />
        <el-table-column label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getMagnetStatusType(scope.row.status)">
              {{ getMagnetStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="150">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.progress || 0"
              :status="scope.row.status === 'failed' ? 'exception' : 'success'"
              :stroke-width="8"
            />
          </template>
        </el-table-column>
        <el-table-column prop="speed" label="速度" width="100" />
        <el-table-column label="创建时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              v-if="scope.row.status === 'failed'"
              size="small"
              type="warning"
              @click="handleRetryMagnet(scope.row._id)"
            >
              重试
            </el-button>
            <el-button
              v-if="['waiting', 'downloading'].includes(scope.row.status)"
              size="small"
              type="danger"
              @click="handleCancelMagnet(scope.row._id)"
            >
              取消
            </el-button>
            <el-button
              v-if="scope.row.status === 'completed'"
              size="small"
              type="success"
              disabled
            >
              已完成
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- JavBus影片列表预览 -->
    <el-card v-if="showPreview" shadow="never" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>影片列表预览</span>
          <div>
            <el-button type="success" @click="handleImportSelected" :disabled="selectedMovies.length === 0" :loading="importing">
              导入选中 ({{ selectedMovies.length }})
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="moviesList" 
        v-loading="loading" 
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="影片ID" width="120" />
        <el-table-column label="封面" width="120">
          <template #default="scope">
            <el-image
              style="width: 75px; height: 100px"
              :src="scope.row.img"
              fit="cover"
              :preview-src-list="[scope.row.img]"
            >
              <template #error>
                <div class="image-error">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="date" label="发行日期" width="120" />
        <el-table-column label="标签" width="150">
          <template #default="scope">
            <div v-if="scope.row.tags && scope.row.tags.length">
              <el-tag v-for="tag in scope.row.tags" :key="tag" size="small" style="margin-right: 5px">
                {{ tag }}
              </el-tag>
            </div>
            <span v-else>无标签</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              size="small"
              type="primary"
              @click="handleViewDetail(scope.row.id)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="100"
          :current-page="importForm.page"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 导入结果 -->
    <el-card v-if="importedMovies.length > 0" shadow="never" class="import-result-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon class="title-icon"><Upload /></el-icon>
            导入结果
          </span>
        </div>
      </template>

      <!-- 导入状态信息 -->
      <div v-if="importStatus && importStatus.isImporting" class="import-status">
        <div class="status-header">
          <el-icon class="status-icon processing"><Loading /></el-icon>
          <h3>正在导入数据...</h3>
        </div>

        <el-progress
          :percentage="importStatus.progress"
          :format="format => `${format}%`"
          status="success"
          :stroke-width="12"
          class="main-progress"
        />

        <div class="import-stats">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="stat-item">
                <div class="stat-label">当前处理:</div>
                <div class="stat-value current-movie">{{ importStatus.currentMovieId || '准备中...' }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">总体进度:</div>
                <div class="stat-value">{{ importStatus.processedMovies }} / {{ importStatus.totalMovies }}</div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="stat-item">
                <div class="stat-label">成功导入:</div>
                <div class="stat-value success-count">{{ importStatus.successfulMovies }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">失败数量:</div>
                <div class="stat-value error-count">{{ importStatus.failedMovies }}</div>
              </div>
            </el-col>
          </el-row>

          <div v-if="importStatus.lastError" class="error-message">
            <el-alert
              :title="importStatus.lastError"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>

      <el-table :data="importedMovies" style="width: 100%">
        <el-table-column prop="id" label="影片ID" width="120" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消息" />
      </el-table>
    </el-card>

    <!-- 影片详情对话框 -->
    <el-dialog 
      title="影片详情" 
      v-model="dialogVisible" 
      width="70%"
      destroy-on-close
    >
      <div v-loading="loadingDetail">
        <div v-if="movieDetail" class="movie-detail">
          <div class="movie-detail-header">
            <div class="movie-poster">
              <el-image
                style="width: 200px; height: 280px"
                :src="movieDetail.img"
                fit="cover"
              />
            </div>
            <div class="movie-info">
              <h3>{{ movieDetail.title }}</h3>
              <div class="movie-meta">
                <p><strong>影片ID:</strong> {{ movieDetail.id }}</p>
                <p><strong>发行日期:</strong> {{ movieDetail.date }}</p>
                <p v-if="movieDetail.length"><strong>时长:</strong> {{ movieDetail.length }}</p>
                <p v-if="movieDetail.director?.name"><strong>导演:</strong> {{ movieDetail.director.name }}</p>
                <p v-if="movieDetail.maker?.name"><strong>制作商:</strong> {{ movieDetail.maker.name }}</p>
                <p v-if="movieDetail.label?.name"><strong>发行商:</strong> {{ movieDetail.label.name }}</p>
              </div>
            </div>
          </div>

          <div class="movie-section" v-if="movieDetail.genres && movieDetail.genres.length">
            <h4>类型</h4>
            <div class="movie-tags">
              <el-tag 
                v-for="genre in movieDetail.genres" 
                :key="genre.id"
                style="margin-right: 10px; margin-bottom: 10px;"
              >
                {{ genre.name }}
              </el-tag>
            </div>
          </div>

          <div class="movie-section" v-if="movieDetail.stars && movieDetail.stars.length">
            <h4>演员</h4>
            <div class="movie-stars">
              <div v-for="star in movieDetail.stars" :key="star.id" class="star-item">
                <el-avatar :size="80" :src="star.img" />
                <div class="star-name">{{ star.name }}</div>
              </div>
            </div>
          </div>

          <div class="movie-section" v-if="movieDetail.samples && movieDetail.samples.length">
            <h4>样品图像</h4>
            <div class="movie-samples">
              <el-image 
                v-for="(sample, index) in movieDetail.samples" 
                :key="index"
                style="width: 180px; height: 120px; margin-right: 10px; margin-bottom: 10px;"
                :src="sample.img"
                fit="cover"
                :preview-src-list="movieDetail.samples.map(s => s.img)"
              />
            </div>
          </div>
        </div>
        <div v-else-if="!loadingDetail" class="empty-data">
          暂无详细数据
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleImportSingle">导入该影片</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Loading, Refresh } from '@element-plus/icons-vue'
import {
  getJavBusMovies,
  getJavBusMovieDetail,
  saveMovies,
  importMoviesFromJavBus,
  downloadMovieImages,
  getImportStatus,
  addCustomMagnetDownload,
  getMagnetDownloadTasks,
  cancelMagnetDownload,
  retryMagnetDownload
} from '../../api/import'

export default {
  name: 'DataImport',
  components: {
    Upload,
    Loading,
    Refresh
  },
  setup() {
    const loading = ref(false)
    const importing = ref(false)
    const loadingDetail = ref(false)
    const updatingReleaseDate = ref(false)
    const dialogVisible = ref(false)
    const showPreview = ref(false)
    const moviesList = ref([])
    const movieDetail = ref(null)
    const importedMovies = ref([])
    const selectedMovies = ref([])
    const importStatus = ref(null)
    const statusTimer = ref(null)

    // 磁力下载相关状态
    const addingMagnet = ref(false)
    const loadingMagnetTasks = ref(false)
    const magnetTasks = ref([])
    const magnetFormRef = ref(null)
    const magnetTasksTimer = ref(null)

    const importForm = reactive({
      page: 1,
      count: 10,
      magnet: 'exist',
      type: 'normal'
    })

    // 磁力下载表单
    const magnetForm = reactive({
      magnetLink: '',
      title: '',
      movieId: '',
      priority: 'medium',
      autoStart: true,
      remark: ''
    })

    // 磁力表单验证规则
    const magnetRules = {
      magnetLink: [
        { required: true, message: '请输入磁力链接', trigger: 'blur' },
        { pattern: /^magnet:\?xt=urn:btih:[a-fA-F0-9]{40}/, message: '请输入有效的磁力链接', trigger: 'blur' }
      ],
      title: [
        { required: true, message: '请输入影片标题', trigger: 'blur' },
        { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
      ]
    }
    
    // 搜索影片列表
    const handleSearch = async () => {
      loading.value = true
      showPreview.value = true
      try {
        const params = {
          page: importForm.page,
          magnet: importForm.magnet,
          type: importForm.type
        }
        
        const res = await getJavBusMovies(params)
        moviesList.value = res.movies || []
      } catch (error) {
        console.error('获取影片列表失败:', error)
        ElMessage.error('获取影片列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 页码变化
    const handlePageChange = (page) => {
      importForm.page = page
      handleSearch()
    }
    
    // 选择变化
    const handleSelectionChange = (selection) => {
      selectedMovies.value = selection
    }
    
    // 查看影片详情
    const handleViewDetail = async (id) => {
      dialogVisible.value = true
      loadingDetail.value = true
      movieDetail.value = null
      
      try {
        const res = await getJavBusMovieDetail(id)
        movieDetail.value = res
      } catch (error) {
        console.error('获取影片详情失败:', error)
        ElMessage.error('获取影片详情失败')
      } finally {
        loadingDetail.value = false
      }
    }
    
    // 开始轮询导入状态
    const startPollingStatus = () => {
      // 清除可能存在的旧定时器
      if (statusTimer.value) {
        clearInterval(statusTimer.value)
      }
      
      // 立即查询一次
      checkImportStatus()
      
      // 设置定时查询
      statusTimer.value = setInterval(() => {
        checkImportStatus()
      }, 5000) // 每5秒查询一次
    }
    
    // 停止轮询
    const stopPollingStatus = () => {
      if (statusTimer.value) {
        clearInterval(statusTimer.value)
        statusTimer.value = null
      }
    }
    
    // 检查导入状态
    const checkImportStatus = async () => {
      try {
        const status = await getImportStatus()
        importStatus.value = status
        
        // 如果导入已完成，停止轮询
        if (!status.isImporting) {
          stopPollingStatus()
        }
      } catch (error) {
        console.error('获取导入状态失败:', error)
      }
    }
    
    // 磁力下载相关方法
    const handleAddMagnetDownload = async () => {
      if (!magnetFormRef.value) return

      try {
        await magnetFormRef.value.validate()
      } catch (error) {
        return
      }

      addingMagnet.value = true

      try {
        const response = await addCustomMagnetDownload({
          magnetLink: magnetForm.magnetLink,
          title: magnetForm.title,
          movieId: magnetForm.movieId || null,
          priority: magnetForm.priority,
          autoStart: magnetForm.autoStart,
          remark: magnetForm.remark
        })

        ElMessage.success('磁力下载任务已添加')
        resetMagnetForm()
        refreshMagnetTasks()
      } catch (error) {
        console.error('添加磁力下载任务失败:', error)
        ElMessage.error('添加磁力下载任务失败: ' + (error.message || '未知错误'))
      } finally {
        addingMagnet.value = false
      }
    }

    const resetMagnetForm = () => {
      Object.assign(magnetForm, {
        magnetLink: '',
        title: '',
        movieId: '',
        priority: 'medium',
        autoStart: true,
        remark: ''
      })
      if (magnetFormRef.value) {
        magnetFormRef.value.clearValidate()
      }
    }

    const refreshMagnetTasks = async () => {
      loadingMagnetTasks.value = true
      try {
        const response = await getMagnetDownloadTasks()
        magnetTasks.value = response.tasks || []
      } catch (error) {
        console.error('获取磁力下载任务失败:', error)
        ElMessage.error('获取磁力下载任务失败')
      } finally {
        loadingMagnetTasks.value = false
      }
    }

    const handleRetryMagnet = async (taskId) => {
      try {
        await retryMagnetDownload(taskId)
        ElMessage.success('重试任务已提交')
        refreshMagnetTasks()
      } catch (error) {
        console.error('重试磁力下载失败:', error)
        ElMessage.error('重试磁力下载失败')
      }
    }

    const handleCancelMagnet = async (taskId) => {
      try {
        await ElMessageBox.confirm('确定要取消这个下载任务吗？', '确认取消', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await cancelMagnetDownload(taskId)
        ElMessage.success('任务已取消')
        refreshMagnetTasks()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消磁力下载失败:', error)
          ElMessage.error('取消磁力下载失败')
        }
      }
    }

    const getMagnetStatusType = (status) => {
      const statusMap = {
        waiting: 'info',
        downloading: 'warning',
        processing: 'warning',
        uploading: 'warning',
        completed: 'success',
        failed: 'danger',
        stopped: 'info'
      }
      return statusMap[status] || 'info'
    }

    const getMagnetStatusText = (status) => {
      const statusMap = {
        waiting: '等待中',
        downloading: '下载中',
        processing: '处理中',
        uploading: '上传中',
        completed: '已完成',
        failed: '失败',
        stopped: '已停止'
      }
      return statusMap[status] || status
    }

    // 格式化时间
    const formatTime = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 开始轮询磁力任务状态
    const startPollingMagnetTasks = () => {
      if (magnetTasksTimer.value) {
        clearInterval(magnetTasksTimer.value)
      }

      refreshMagnetTasks()
      magnetTasksTimer.value = setInterval(() => {
        refreshMagnetTasks()
      }, 3000) // 每3秒刷新一次
    }

    const stopPollingMagnetTasks = () => {
      if (magnetTasksTimer.value) {
        clearInterval(magnetTasksTimer.value)
        magnetTasksTimer.value = null
      }
    }

    // 在组件卸载时清除定时器
    onMounted(() => {
      checkImportStatus() // 初始检查
      startPollingMagnetTasks() // 开始轮询磁力任务
    })

    onUnmounted(() => {
      stopPollingStatus()
      stopPollingMagnetTasks()
    })
    
    // 导入全部
    const handleImport = async () => {
      importing.value = true
      importedMovies.value = []
      
      try {
        const response = await importMoviesFromJavBus({
          page: importForm.page,
          count: importForm.count,
          magnet: importForm.magnet,
          type: importForm.type,
          downloadImages: true
        })
        
        // 后端返回的是异步任务状态，不是结果数组
        if (response && response.taskId) {
          ElMessage.success(`成功提交导入任务，共${response.totalMovies}部影片，正在后台处理...`)
          
          // 开始轮询导入状态
          startPollingStatus()
          
          importedMovies.value = [{
            id: 'task-' + response.taskId,
            title: '批量导入任务',
            status: 'success',
            message: `已提交${response.totalMovies}部影片的导入任务`
          }]
        } else {
          ElMessage.warning('提交导入任务成功，但未收到任务ID')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        importing.value = false
      }
    }
    
    // 导入选中的影片
    const handleImportSelected = async () => {
      if (selectedMovies.value.length === 0) {
        ElMessage.warning('请选择要导入的影片')
        return
      }
      
      importing.value = true
      importedMovies.value = []
      
      try {
        // 获取选中影片的详情
        const detailedMovies = []
        for (const movie of selectedMovies.value) {
          try {
            ElMessage.info(`正在获取影片 ${movie.id} 详情`)
            const detail = await getJavBusMovieDetail(movie.id)

            // 获取磁力链接
            ElMessage.info(`正在获取影片 ${movie.id} 磁力链接`)
            try {
              const magnetsResponse = await fetch(`http://localhost:3000/api/magnets/${movie.id}?gid=${detail.gid}&uc=${detail.uc}`)
              if (magnetsResponse.ok) {
                const magnets = await magnetsResponse.json()
                detail.magnets = magnets || []
                console.log(`影片 ${movie.id} 获取到 ${detail.magnets.length} 个磁力链接`)
              } else {
                console.warn(`获取影片 ${movie.id} 磁力链接失败`)
                detail.magnets = []
              }
            } catch (magnetError) {
              console.error(`获取影片 ${movie.id} 磁力链接出错:`, magnetError)
              detail.magnets = []
            }

            // 请求下载图片
            ElMessage.info(`下载影片 ${movie.id} 的图片资源`)
            await downloadMovieImages({
              movieId: movie.id,
              coverImage: detail.img,
              sampleImages: detail.samples ? detail.samples.map(s => s.img) : [],
              starImages: detail.stars ? detail.stars.map(s => ({ id: s.id, image: s.img })) : []
            })

            detailedMovies.push(detail)
          } catch (error) {
            console.error(`获取影片 ${movie.id} 详情失败:`, error)
            ElMessage.error(`获取影片 ${movie.id} 详情失败`)
          }
        }
        
        // 保存到数据库
        ElMessage.info('正在保存数据到数据库')
        const response = await saveMovies({ 
          movies: detailedMovies,
          downloadImages: true // 告诉后端需要处理图片
        })
        
        // 后端返回的是异步任务状态，不是结果数组
        if (response && response.taskId) {
          ElMessage.success(`成功提交导入任务，共${response.totalMovies}部影片，正在后台处理...`)
          
          // 开始轮询导入状态
          startPollingStatus()
          
          importedMovies.value = [{
            id: 'task-' + response.taskId,
            title: `选中影片导入任务`,
            status: 'success',
            message: `已提交${response.totalMovies}部影片的导入任务`
          }]
        } else {
          ElMessage.warning('提交导入任务成功，但未收到任务ID')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        importing.value = false
      }
    }
    
    // 导入单个影片
    const handleImportSingle = async () => {
      if (!movieDetail.value) {
        ElMessage.warning('影片详情不存在')
        return
      }
      
      importing.value = true
      
      try {
        const movie = movieDetail.value

        // 获取磁力链接
        ElMessage.info(`正在获取影片 ${movie.id} 磁力链接`)
        try {
          const magnetsResponse = await fetch(`http://localhost:3000/api/magnets/${movie.id}?gid=${movie.gid}&uc=${movie.uc}`)
          if (magnetsResponse.ok) {
            const magnets = await magnetsResponse.json()
            movie.magnets = magnets || []
            console.log(`影片 ${movie.id} 获取到 ${movie.magnets.length} 个磁力链接`)
          } else {
            console.warn(`获取影片 ${movie.id} 磁力链接失败`)
            movie.magnets = []
          }
        } catch (magnetError) {
          console.error(`获取影片 ${movie.id} 磁力链接出错:`, magnetError)
          movie.magnets = []
        }

        // 请求下载图片
        ElMessage.info(`下载影片 ${movie.id} 的图片资源`)
        await downloadMovieImages({
          movieId: movie.id,
          coverImage: movie.img,
          sampleImages: movie.samples ? movie.samples.map(s => s.img) : [],
          starImages: movie.stars ? movie.stars.map(s => ({ id: s.id, image: s.img })) : []
        })

        // 保存到数据库
        ElMessage.info('正在保存数据到数据库')
        const response = await saveMovies({
          movies: [movie],
          downloadImages: true // 告诉后端需要处理图片
        })
        
        // 后端返回的是异步任务状态，不是结果数组
        if (response && response.taskId) {
          ElMessage.success(`成功提交影片 ${movie.id} 的导入任务，正在后台处理...`)
          
          // 开始轮询导入状态
          startPollingStatus()
          
          importedMovies.value = [{
            id: 'task-' + response.taskId,
            title: movie.title || movie.id,
            status: 'success',
            message: `已提交导入任务`
          }]
          
          dialogVisible.value = false
        } else {
          ElMessage.warning('提交导入任务成功，但未收到任务ID')
        }
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error('导入失败: ' + (error.message || '未知错误'))
      } finally {
        importing.value = false
      }
    }

    // 更新发行日期
    const handleUpdateReleaseDate = async () => {
      try {
        const confirm = await ElMessageBox.confirm(
          '此操作将扫描所有有磁力链接的影片，将磁力链接中最早的分享日期设置为作品发行日期。是否继续？',
          '确认更新发行日期',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        
        if (confirm !== 'confirm') {
          return
        }
        
        updatingReleaseDate.value = true
        ElMessage.info('正在更新发行日期，请稍候...')
        
        const response = await fetch('/api/javbus-admin/update-release-date', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const result = await response.json()
        
        if (result.success) {
          ElMessage.success(`更新完成！处理了 ${result.data.processed} 部影片，成功更新 ${result.data.updated} 部`)
          
          // 显示详细统计
          ElMessageBox.alert(
            `处理影片总数: ${result.data.processed}\n成功更新: ${result.data.updated}\n跳过: ${result.data.skipped}\n错误: ${result.data.errors}`,
            '更新统计',
            {
              confirmButtonText: '确定',
              type: 'info'
            }
          )
        } else {
          throw new Error(result.message || '更新失败')
        }
        
      } catch (error) {
        console.error('更新发行日期失败:', error)
        ElMessage.error('更新发行日期失败: ' + (error.message || '未知错误'))
      } finally {
        updatingReleaseDate.value = false
      }
    }
    
    return {
      loading,
      importing,
      loadingDetail,
      updatingReleaseDate,
      dialogVisible,
      showPreview,
      moviesList,
      movieDetail,
      importForm,
      importedMovies,
      selectedMovies,
      importStatus,
      checkImportStatus,
      handleSearch,
      handlePageChange,
      handleSelectionChange,
      handleViewDetail,
      handleImport,
      handleImportSelected,
      handleImportSingle,
      handleUpdateReleaseDate,
      // 磁力下载相关
      addingMagnet,
      loadingMagnetTasks,
      magnetTasks,
      magnetForm,
      magnetRules,
      magnetFormRef,
      handleAddMagnetDownload,
      resetMagnetForm,
      refreshMagnetTasks,
      handleRetryMagnet,
      handleCancelMagnet,
      getMagnetStatusType,
      getMagnetStatusText,
      formatTime
    }
  }
}
</script>

<style scoped>
.import-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 120px);
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 14px;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

:deep(.el-card:hover) {
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 20px;
  color: #409eff;
}

/* 导入结果卡片 */
.import-result-card {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

/* 导入状态样式 */
.import-status {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  border: 1px solid #e1f5fe;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.status-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.status-icon {
  font-size: 24px;
}

.status-icon.processing {
  color: #409eff;
  animation: pulse 2s infinite;
}

.main-progress {
  margin-bottom: 20px;
}

.import-stats {
  margin-top: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateX(5px);
}

.stat-label {
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

.stat-value {
  font-weight: 700;
  font-size: 16px;
  color: #2c3e50;
}

.current-movie {
  color: #409eff;
  font-family: monospace;
}

.success-count {
  color: #67c23a;
}

.error-count {
  color: #f56c6c;
}

.error-message {
  margin-top: 15px;
}

/* 电影详情样式 */
.movie-detail {
  padding: 20px;
}

.movie-detail-header {
  display: flex;
  margin-bottom: 20px;
}

.movie-poster {
  margin-right: 20px;
}

.movie-info {
  flex: 1;
}

.movie-meta p {
  margin: 8px 0;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.movie-section {
  margin-top: 20px;
}

.movie-stars {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.star-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.star-item:hover {
  background: #e3f2fd;
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.star-name {
  margin-top: 10px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.image-error {
  width: 75px;
  height: 100px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8eaf6 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  border-radius: 6px;
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-size: 16px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #2c3e50;
  font-weight: 600;
}

:deep(.el-table tr:hover) {
  background: #f0f9ff;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 10px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.3);
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.import-status {
  animation: slideInUp 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .import-container {
    padding: 10px;
  }

  .movie-detail-header {
    flex-direction: column;
    text-align: center;
  }

  .movie-poster {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .movie-stars {
    justify-content: center;
  }

  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}
</style>