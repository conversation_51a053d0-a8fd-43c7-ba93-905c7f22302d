<template>
  <div class="data-collector">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>📡 外网数据采集管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="collectNow" :loading="collecting">
              <i class="el-icon-download"></i> 立即采集
            </el-button>
            <el-button @click="refreshStatus">
              <i class="el-icon-refresh"></i> 刷新状态
            </el-button>
          </div>
        </div>
      </template>

      <!-- 服务状态 -->
      <div class="status-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="status-card">
              <div class="status-content">
                <div class="status-icon" :class="apiStatus.class">
                  <i :class="apiStatus.icon"></i>
                </div>
                <div class="status-info">
                  <div class="status-title">外网API</div>
                  <div class="status-text">{{ apiStatus.text }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="status-card">
              <div class="status-content">
                <div class="status-icon" :class="collectorStatus.class">
                  <i :class="collectorStatus.icon"></i>
                </div>
                <div class="status-info">
                  <div class="status-title">采集服务</div>
                  <div class="status-text">{{ collectorStatus.text }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="status-card">
              <div class="status-content">
                <div class="status-icon success">
                  <i class="el-icon-data-line"></i>
                </div>
                <div class="status-info">
                  <div class="status-title">总影片数</div>
                  <div class="status-text">{{ stats.totalMovies }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="status-card">
              <div class="status-content">
                <div class="status-icon info">
                  <i class="el-icon-time"></i>
                </div>
                <div class="status-info">
                  <div class="status-title">最后采集</div>
                  <div class="status-text">{{ lastCollectionTime }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 采集配置 -->
      <div class="config-section">
        <h3>📋 采集配置</h3>
        <el-form :model="config" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="批量大小">
                <el-input-number v-model="config.batchSize" :min="10" :max="100" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采集间隔">
                <el-select v-model="config.interval" placeholder="选择采集间隔">
                  <el-option label="每30分钟" value="*/30 * * * *" />
                  <el-option label="每小时" value="0 */1 * * *" />
                  <el-option label="每2小时" value="0 */2 * * *" />
                  <el-option label="每天凌晨2点" value="0 2 * * *" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="自动发布">
                <el-switch v-model="config.autoPublish" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="跳过重复">
                <el-switch v-model="config.skipDuplicates" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="saveConfig">保存配置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 采集日志 -->
      <div class="logs-section">
        <h3>📝 采集日志</h3>
        <div class="log-controls">
          <el-button size="small" @click="clearLogs">清空日志</el-button>
          <el-button size="small" @click="downloadLogs">下载日志</el-button>
        </div>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            :class="['log-item', log.level]"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="stats-section">
        <h3>📊 数据统计</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-number">{{ stats.todayCollected }}</div>
              <div class="stat-label">今日采集</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-number">{{ stats.weekCollected }}</div>
              <div class="stat-label">本周采集</div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="stat-card">
              <div class="stat-number">{{ stats.monthCollected }}</div>
              <div class="stat-label">本月采集</div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'DataCollector',
  data() {
    return {
      collecting: false,
      apiStatus: {
        text: '检查中...',
        class: 'warning',
        icon: 'el-icon-loading'
      },
      collectorStatus: {
        text: '检查中...',
        class: 'warning', 
        icon: 'el-icon-loading'
      },
      stats: {
        totalMovies: 0,
        todayCollected: 0,
        weekCollected: 0,
        monthCollected: 0
      },
      lastCollectionTime: '未知',
      config: {
        batchSize: 50,
        interval: '0 */1 * * *',
        autoPublish: true,
        skipDuplicates: true
      },
      logs: []
    };
  },
  mounted() {
    this.checkApiStatus();
    this.checkCollectorStatus();
    this.loadStats();
    this.loadLogs();
    this.loadConfig();
    
    // 定时刷新状态
    this.statusTimer = setInterval(() => {
      this.checkApiStatus();
      this.checkCollectorStatus();
    }, 30000);
  },
  beforeUnmount() {
    if (this.statusTimer) {
      clearInterval(this.statusTimer);
    }
  },
  methods: {
    async checkApiStatus() {
      try {
        const response = await this.$http.get('/api/collector/external-status');
        if (response.data.success) {
          this.apiStatus = {
            text: '正常',
            class: 'success',
            icon: 'el-icon-success'
          };
        }
      } catch (error) {
        this.apiStatus = {
          text: '异常',
          class: 'danger',
          icon: 'el-icon-error'
        };
      }
    },

    async checkCollectorStatus() {
      try {
        const response = await this.$http.get('/api/collector/status');
        if (response.data.success) {
          this.collectorStatus = {
            text: response.data.data.status === 'running' ? '运行中' : '已停止',
            class: response.data.data.status === 'running' ? 'success' : 'warning',
            icon: response.data.data.status === 'running' ? 'el-icon-success' : 'el-icon-warning'
          };
          this.lastCollectionTime = this.formatTime(response.data.data.lastCollection);
        }
      } catch (error) {
        this.collectorStatus = {
          text: '未知',
          class: 'danger',
          icon: 'el-icon-error'
        };
      }
    },

    async collectNow() {
      this.collecting = true;
      try {
        const response = await this.$http.post('/api/collector/collect-now');
        if (response.data.success) {
          this.$message.success('数据采集已开始');
          this.loadLogs();
        }
      } catch (error) {
        this.$message.error('启动采集失败: ' + error.message);
      } finally {
        this.collecting = false;
      }
    },

    async loadStats() {
      try {
        const response = await this.$http.get('/api/collector/stats');
        if (response.data.success) {
          this.stats = response.data.data;
        }
      } catch (error) {
        console.error('加载统计失败:', error);
      }
    },

    async loadLogs() {
      try {
        const response = await this.$http.get('/api/collector/logs');
        if (response.data.success) {
          this.logs = response.data.data;
        }
      } catch (error) {
        console.error('加载日志失败:', error);
      }
    },

    async loadConfig() {
      try {
        const response = await this.$http.get('/api/collector/config');
        if (response.data.success) {
          this.config = { ...this.config, ...response.data.data };
        }
      } catch (error) {
        console.error('加载配置失败:', error);
      }
    },

    async saveConfig() {
      try {
        const response = await this.$http.post('/api/collector/config', this.config);
        if (response.data.success) {
          this.$message.success('配置保存成功');
        }
      } catch (error) {
        this.$message.error('配置保存失败: ' + error.message);
      }
    },

    refreshStatus() {
      this.checkApiStatus();
      this.checkCollectorStatus();
      this.loadStats();
      this.loadLogs();
    },

    clearLogs() {
      this.$confirm('确定要清空所有日志吗？', '确认', {
        type: 'warning'
      }).then(async () => {
        try {
          await this.$http.delete('/api/collector/logs');
          this.logs = [];
          this.$message.success('日志已清空');
        } catch (error) {
          this.$message.error('清空日志失败');
        }
      });
    },

    downloadLogs() {
      window.open('/api/collector/logs/download', '_blank');
    },

    formatTime(timestamp) {
      if (!timestamp) return '未知';
      return new Date(timestamp).toLocaleString('zh-CN');
    }
  }
};
</script>

<style scoped>
.data-collector {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-section {
  margin-bottom: 30px;
}

.status-card {
  text-align: center;
}

.status-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.status-icon.success {
  background: #f0f9ff;
  color: #67c23a;
}

.status-icon.warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.status-icon.danger {
  background: #fef0f0;
  color: #f56c6c;
}

.status-icon.info {
  background: #f4f4f5;
  color: #909399;
}

.status-info {
  text-align: left;
}

.status-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.status-text {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.config-section,
.logs-section,
.stats-section {
  margin-top: 30px;
}

.log-controls {
  margin-bottom: 15px;
  text-align: right;
}

.log-container {
  height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  min-width: 150px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-level.info {
  color: #409eff;
}

.log-level.error {
  color: #f56c6c;
}

.log-level.warn {
  color: #e6a23c;
}

.log-message {
  flex: 1;
  color: #303133;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>
