require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = process.env.PORT || 4000;

// 创建PostgreSQL连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'postgres',
  password: process.env.PGPASSWORD || '',
});

// 中间件
app.use(cors());
app.use(express.json());

// 基础路由
app.get('/', (req, res) => {
  res.json({ message: 'JAVFLIX 简化API服务运行中' });
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 获取影片列表（直接查询movies表）
app.get('/api/published-videos', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // 直接查询movies表，不需要status字段
    const query = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.release_date,
        m.duration,
        m.description,
        m.view_count,
        m.created_at,
        m.updated_at
      FROM movies m
      ORDER BY m.created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const result = await pool.query(query, [limit, offset]);

    // 获取总数
    const countResult = await pool.query('SELECT COUNT(*) as total FROM movies');
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: {
        movies: result.rows,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      },
      message: '获取影片列表成功'
    });

  } catch (error) {
    console.error('获取影片列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取影片列表失败',
      error: error.message
    });
  }
});

// 获取热门影片
app.get('/api/published-videos/popular', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    const query = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.view_count as views,
        m.release_date,
        m.created_at
      FROM movies m
      ORDER BY m.view_count DESC NULLS LAST, m.created_at DESC
      LIMIT $1
    `;

    const result = await pool.query(query, [limit]);

    res.json({
      success: true,
      data: result.rows,
      message: '获取热门影片成功'
    });

  } catch (error) {
    console.error('获取热门影片失败:', error);
    res.status(500).json({
      success: false,
      message: '获取热门影片失败',
      error: error.message
    });
  }
});

// 获取最新影片
app.get('/api/published-videos/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    const query = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.release_date,
        m.created_at
      FROM movies m
      ORDER BY m.created_at DESC
      LIMIT $1
    `;

    const result = await pool.query(query, [limit]);

    res.json({
      success: true,
      data: result.rows,
      message: '获取最新影片成功'
    });

  } catch (error) {
    console.error('获取最新影片失败:', error);
    res.status(500).json({
      success: false,
      message: '获取最新影片失败',
      error: error.message
    });
  }
});

// 搜索影片
app.get('/api/published-videos/search', async (req, res) => {
  try {
    const { q: query } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    if (!query) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }

    const searchQuery = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.release_date,
        m.duration,
        m.description,
        m.view_count,
        m.created_at
      FROM movies m
      WHERE m.title ILIKE $1 OR m.movie_id ILIKE $1
      ORDER BY m.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const result = await pool.query(searchQuery, [`%${query}%`, limit, offset]);

    // 获取搜索结果总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM movies m
      WHERE m.title ILIKE $1 OR m.movie_id ILIKE $1
    `;
    const countResult = await pool.query(countQuery, [`%${query}%`]);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: {
        movies: result.rows,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      },
      message: '搜索影片成功'
    });

  } catch (error) {
    console.error('搜索影片失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索影片失败',
      error: error.message
    });
  }
});

// 获取演员列表
app.get('/api/db/stars', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const query = `
      SELECT 
        s.id,
        s.star_id,
        s.name,
        s.image_url,
        s.cached_image_url,
        s.created_at
      FROM stars s
      ORDER BY s.created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const result = await pool.query(query, [limit, offset]);

    // 获取总数
    const countResult = await pool.query('SELECT COUNT(*) as total FROM stars');
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: {
        items: result.rows,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      },
      message: '获取演员列表成功'
    });

  } catch (error) {
    console.error('获取演员列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取演员列表失败',
      error: error.message
    });
  }
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const result = await pool.query('SELECT NOW()');
    console.log(`✅ PostgreSQL 连接成功: ${result.rows[0].now}`);

    app.listen(PORT, () => {
      console.log(`🚀 简化API服务器运行在端口 ${PORT}`);
      console.log(`📡 API地址: http://localhost:${PORT}`);
    });

  } catch (error) {
    console.error(`❌ 启动失败: ${error.message}`);
    process.exit(1);
  }
}

startServer();
