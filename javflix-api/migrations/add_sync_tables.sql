-- 数据同步管理数据库迁移
-- 添加外网API数据同步相关的表结构
-- 创建时间: 2024-07-24

-- 1. 创建同步状态管理表
CREATE TABLE IF NOT EXISTS sync_status (
  id SERIAL PRIMARY KEY,
  entity_type VARCHAR(50) NOT NULL,  -- 实体类型: 'movies', 'actors', 'genres'
  last_sync_time TIMESTAMP,          -- 上次同步时间
  total_synced INTEGER DEFAULT 0,    -- 总同步数量
  errors_count INTEGER DEFAULT 0,    -- 错误数量
  status VARCHAR(20) DEFAULT 'idle', -- 同步状态: 'idle', 'running', 'completed', 'failed'
  sync_config JSONB DEFAULT '{}',    -- 同步配置信息
  last_error_message TEXT,           -- 最后错误信息
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. 创建同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
  id SERIAL PRIMARY KEY,
  entity_type VARCHAR(50) NOT NULL,  -- 实体类型
  entity_id VARCHAR(100),            -- 实体ID（外网API的ID）
  local_id INTEGER,                  -- 本地数据库ID
  action VARCHAR(20) NOT NULL,       -- 操作类型: 'create', 'update', 'skip', 'error'
  details JSONB DEFAULT '{}',        -- 详细信息
  error_message TEXT,                -- 错误信息（如果有）
  processing_time INTEGER,           -- 处理时间（毫秒）
  created_at TIMESTAMP DEFAULT NOW()
);

-- 3. 添加唯一约束和索引
-- sync_status表索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_sync_status_entity_type ON sync_status(entity_type);
CREATE INDEX IF NOT EXISTS idx_sync_status_updated_at ON sync_status(updated_at);
CREATE INDEX IF NOT EXISTS idx_sync_status_status ON sync_status(status);

-- sync_logs表索引
CREATE INDEX IF NOT EXISTS idx_sync_logs_entity_type ON sync_logs(entity_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_entity_id ON sync_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_local_id ON sync_logs(local_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_action ON sync_logs(action);
CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at ON sync_logs(created_at);

-- 4. 创建状态枚举约束
ALTER TABLE sync_status ADD CONSTRAINT sync_status_status_check 
CHECK (status IN ('idle', 'running', 'completed', 'failed', 'paused'));

ALTER TABLE sync_logs ADD CONSTRAINT sync_logs_action_check 
CHECK (action IN ('create', 'update', 'skip', 'error', 'delete'));

-- 5. 初始化默认同步状态记录
INSERT INTO sync_status (entity_type, status, sync_config) VALUES 
('movies', 'idle', '{"api_endpoint": "/api/v1/movies", "batch_size": 100}'),
('actors', 'idle', '{"api_endpoint": "/api/v1/actors", "batch_size": 50}'),
('genres', 'idle', '{"api_endpoint": "/api/v1/genres", "batch_size": 20}')
ON CONFLICT (entity_type) DO NOTHING;

-- 6. 创建自动更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_sync_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS sync_status_update_trigger ON sync_status;
CREATE TRIGGER sync_status_update_trigger
    BEFORE UPDATE ON sync_status
    FOR EACH ROW
    EXECUTE FUNCTION update_sync_status_timestamp();

-- 7. 创建同步统计视图
CREATE OR REPLACE VIEW sync_statistics AS
SELECT
    ss.entity_type,
    ss.status,
    ss.last_sync_time,
    ss.total_synced,
    ss.errors_count,
    COALESCE(recent_logs.recent_success, 0) as recent_success_count,
    COALESCE(recent_logs.recent_errors, 0) as recent_error_count,
    CASE
        WHEN ss.total_synced > 0 THEN
            ROUND((ss.total_synced - ss.errors_count) * 100.0 / ss.total_synced, 2)
        ELSE 0
    END as success_rate
FROM sync_status ss
LEFT JOIN (
    SELECT
        entity_type,
        COUNT(CASE WHEN action IN ('create', 'update') THEN 1 END) as recent_success,
        COUNT(CASE WHEN action = 'error' THEN 1 END) as recent_errors
    FROM sync_logs
    WHERE created_at >= NOW() - INTERVAL '24 hours'
    GROUP BY entity_type
) recent_logs ON ss.entity_type = recent_logs.entity_type;

-- 8. 添加数据清理函数（保留最近30天的日志）
CREATE OR REPLACE FUNCTION cleanup_old_sync_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM sync_logs
    WHERE created_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    INSERT INTO sync_logs (entity_type, action, details) VALUES
    ('system', 'cleanup', jsonb_build_object('deleted_count', deleted_count));

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 9. 创建同步性能监控表
CREATE TABLE IF NOT EXISTS sync_performance_metrics (
  id SERIAL PRIMARY KEY,
  entity_type VARCHAR(50) NOT NULL,
  metric_name VARCHAR(50) NOT NULL,    -- 'api_response_time', 'processing_time', 'batch_size'
  metric_value NUMERIC NOT NULL,
  recorded_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_sync_metrics_entity_type ON sync_performance_metrics(entity_type);
CREATE INDEX IF NOT EXISTS idx_sync_metrics_name ON sync_performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_sync_metrics_recorded_at ON sync_performance_metrics(recorded_at);

-- 10. 添加注释说明
COMMENT ON TABLE sync_status IS '数据同步状态管理表，记录各实体类型的同步状态和配置';
COMMENT ON TABLE sync_logs IS '数据同步操作日志表，记录每次同步操作的详细信息';
COMMENT ON TABLE sync_performance_metrics IS '同步性能指标表，记录API响应时间等性能数据';
COMMENT ON VIEW sync_statistics IS '同步统计视图，提供各实体类型的同步统计信息';
