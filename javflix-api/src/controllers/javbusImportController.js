const axios = require('axios');
const fs = require('fs');
const path = require('path');
const apiResponse = require('../utils/apiResponse');
const { Pool } = require('pg');
const slugify = require('slugify');
const javbusController = require('./javbusController');
const https = require('https');
const crypto = require('crypto');

// 配置axios全局超时 - 支持大规模采集
axios.defaults.timeout = 60000; // 增加到60秒超时
axios.defaults.headers.common['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

// 配置数据库连接
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
  // 大规模采集优化配置
  max: 20, // 增加最大连接数
  idleTimeoutMillis: 30000, // 空闲超时30秒
  connectionTimeoutMillis: 10000, // 连接超时10秒
  query_timeout: 60000, // 查询超时60秒
});

// JavBus API URL
const JAVBUS_API_URL = 'http://localhost:3000/api';

// 定义图片保存的路径
const IMAGE_BASE_DIR = path.join(__dirname, '../../../javflix/public/images/javbus');

// 导入状态
const importStatus = {
  isImporting: false,
  totalMovies: 0,
  processedMovies: 0,
  successfulMovies: 0,
  failedMovies: 0,
  startTime: null,
  endTime: null,
  lastError: null,
  currentMovieId: null
};

/**
 * 获取哈希文件名
 */
function getHashedFileName(url) {
  const hash = crypto.createHash('md5').update(url).digest('hex');
  const ext = path.extname(url) || '.jpg'; // 默认为jpg
  return `${hash}${ext}`;
}

/**
 * 确保目录存在
 */
function ensureDirExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 将缩略图URL转换为高清封面URL
 */
function convertToHighResUrl(url) {
  if (!url || typeof url !== 'string') return url;
  
  // 常见的javbus缩略图URL格式处理
  if (url.includes('/pics/thumb/')) {
    // 提取文件名部分
    const parts = url.split('/');
    const filename = parts[parts.length - 1];
    
    // 分离文件名和扩展名
    const dotIndex = filename.lastIndexOf('.');
    const name = dotIndex > 0 ? filename.substring(0, dotIndex) : filename;
    const ext = dotIndex > 0 ? filename.substring(dotIndex) : '';
    
    // 替换目录并添加_b后缀
    return url.replace('/pics/thumb/', '/pics/cover/').replace(filename, `${name}_b${ext}`);
  }
  
  // 处理其他可能的URL格式
  // 有些javbus图片可能使用不同的目录结构
  if (url.includes('/thumbs/') && !url.includes('_b')) {
    return url.replace('/thumbs/', '/cover/').replace('.jpg', '_b.jpg');
  }
  
  return url;
}

/**
 * 下载图片并保存到本地
 */
async function downloadImage(url, type = 'cover') {
  if (!url) return null;
  
  // 如果是封面图片，转换为高清版本
  if (type === 'cover') {
    url = convertToHighResUrl(url);
  }
  
  console.log(`正在下载图片: ${url}`);
  const imageDirPath = path.join(IMAGE_BASE_DIR, type);
  ensureDirExists(imageDirPath);
  
  const hashedFileName = getHashedFileName(url);
  const filePath = path.join(imageDirPath, hashedFileName);
  const publicPath = `/images/javbus/${type}/${hashedFileName}`;
  
  // 如果文件已存在，直接返回路径
  if (fs.existsSync(filePath)) {
    console.log(`图片已存在，跳过下载: ${publicPath}`);
    return publicPath;
  }
  
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filePath);
    
    const request = https.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.javbus.com/'
      },
      timeout: 60000 // 增加到60秒超时，支持大规模采集
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(filePath, () => {}); // 删除可能部分下载的文件
        console.error(`下载失败，状态码: ${response.statusCode}`);
        resolve(null); // 下载失败返回null，不使用原始URL
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`图片下载成功: ${publicPath}`);
        resolve(publicPath);
      });
    });
    
    request.on('error', (err) => {
      file.close();
      fs.unlink(filePath, () => {});
      console.error('下载图片出错:', err.message);
      resolve(null); // 下载失败返回null，不使用原始URL
    });
    
    request.on('timeout', () => {
      request.destroy();
      file.close();
      fs.unlink(filePath, () => {});
      console.error(`下载图片超时 (60秒): ${url}`);
      resolve(null);
    });
    
    // 添加额外的超时保护
    const timeoutId = setTimeout(() => {
      if (!request.destroyed) {
        request.destroy();
        file.close();
        fs.unlink(filePath, () => {});
        console.error(`下载图片超时保护触发 (60秒): ${url}`);
        resolve(null);
      }
    }, 60000);
    
    // 清理定时器
    request.on('close', () => clearTimeout(timeoutId));
    request.on('end', () => clearTimeout(timeoutId));
  });
}

/**
 * 验证女优名字是否有效（过滤HTML标签和异常内容）
 */
function isValidStarName(name) {
  if (!name || typeof name !== 'string') return false;
  
  // 去除首尾空格
  name = name.trim();
  
  // 如果为空，无效
  if (name.length === 0) return false;
  
  // 包含HTML标签，无效
  if (name.includes('<') || name.includes('>')) return false;
  
  // 是常见的无效名称，无效
  const invalidNames = [
    '画像を拡大する',
    'img',
    'i',
    'div',
    'span',
    'script',
    'style',
    'br',
    'p',
    'a',
    'button',
    'input'
  ];
  
  if (invalidNames.includes(name.toLowerCase())) return false;
  
  // 长度异常（太短或太长），无效
  if (name.length < 2 || name.length > 50) return false;
  
  // 检查是否只包含纯符号或纯数字（允许日文字符）
  // 日文字符范围：平假名(\u3040-\u309F)、片假名(\u30A0-\u30FF)、汉字(\u4E00-\u9FAF)
  // 允许的字符：日文字符、英文字母、数字、常见符号（空格、点、中划线等）
  const validCharPattern = /^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\w\s\.\-\u00C0-\u024F\u1E00-\u1EFF]+$/;
  
  if (!validCharPattern.test(name)) {
    console.log(`女优名字包含异常字符: "${name}"`);
    return false;
  }
  
  // 检查是否只包含纯数字或纯符号（但允许日文字符）
  const pureSymbolsOrNumbers = /^[\d\W]*$/.test(name) && !/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\w]/.test(name);
  if (pureSymbolsOrNumbers) {
    console.log(`女优名字只包含数字或符号: "${name}"`);
    return false;
  }
  
  return true;
}

/**
 * 从JavBus导入影片数据到数据库
 */
/**
 * 解析分享日期字符串（在导入时使用）
 */
function parseShareDate(shareDate) {
  if (!shareDate || typeof shareDate !== 'string') {
    return null;
  }
  
  // 支持多种日期格式
  const dateFormats = [
    /^(\d{4}-\d{2}-\d{2})$/,           // 2025-04-28
    /^(\d{4})\/(\d{2})\/(\d{2})$/,     // 2025/04/28
    /^(\d{2})\/(\d{2})\/(\d{4})$/,     // 28/04/2025
    /^(\d{4})年(\d{1,2})月(\d{1,2})日$/ // 2025年4月28日
  ];
  
  // 标准ISO格式
  if (dateFormats[0].test(shareDate)) {
    const date = new Date(shareDate);
    return isNaN(date.getTime()) ? null : date;
  }
  
  // 其他格式的解析可以在这里添加
  // 简单起见，先尝试直接解析
  const date = new Date(shareDate);
  return isNaN(date.getTime()) ? null : date;
}

exports.importMovies = async (req, res) => {
  const { count = 10, page = 1, downloadImages = true } = req.query;
  const maxPages = parseInt(page) || 1; // 最大采集页数
  const countPerPage = parseInt(count) || 10; // 每页采集数量
  
  try {
    console.log(`准备从第1页采集到第${maxPages}页，每页最多${countPerPage}部影片`);
    console.log(`下载图片设置: ${downloadImages}`);
    console.log(`JavBus API URL: ${JAVBUS_API_URL}`);
    
    // 测试数据库连接
    console.log('测试数据库连接...');
    const testClient = await pool.connect();
    await testClient.query('SELECT 1');
    testClient.release();
    console.log('数据库连接成功');
    
    // 测试JavBus API连接
    console.log('测试JavBus API连接...');
    const testResponse = await axios.get(`${JAVBUS_API_URL}/movies?page=1`);
    console.log(`JavBus API响应状态: ${testResponse.status}`);
    console.log(`JavBus API返回影片数量: ${testResponse.data.movies ? testResponse.data.movies.length : 0}`);
    
    // 初始化统计数据
    const importedMovies = [];
    let totalMoviesFetched = 0;
    let censoredCount = 0;
    let uncensoredCount = 0;
    
    // 确保图片目录存在
    ensureDirExists(IMAGE_BASE_DIR);
    
    // 确保数据库表和约束存在
    console.log('初始化数据库表结构...');
    const initClient = await pool.connect();
    try {
      await createTablesIfNotExist(initClient);
      console.log('数据库表结构初始化完成');
    } finally {
      initClient.release();
    }
    
    // 循环采集每一页
    for (let currentPage = 1; currentPage <= maxPages; currentPage++) {
      try {
        console.log(`\n=== 正在采集第 ${currentPage}/${maxPages} 页 ===`);
        
        // 1. 从JavBus API获取当前页影片列表
        const javbusResponse = await axios.get(`${JAVBUS_API_URL}/movies?page=${currentPage}&magnet=exist`);
        let movies = javbusResponse.data.movies || [];
        
        if (movies.length === 0) {
          console.log(`第${currentPage}页没有数据，停止采集`);
          break;
        }
        
        // 如果指定了每页数量限制，则只取前N个
        if (countPerPage > 0 && countPerPage < movies.length) {
          movies = movies.slice(0, countPerPage);
        }
        
        console.log(`第${currentPage}页获取到${movies.length}部影片`);
        totalMoviesFetched += movies.length;
        
        // 2. 遍历当前页的每部影片，获取详情
        for (const movie of movies) {
          try {
            console.log(`\n处理影片: ${movie.id} (第${currentPage}页)`);
            
            // 获取影片详情
            const detailResponse = await axios.get(`${JAVBUS_API_URL}/movies/${movie.id}`);
            const movieDetail = detailResponse.data;
            
            // 获取磁力链接
            const magnetsResponse = await axios.get(`${JAVBUS_API_URL}/magnets/${movie.id}`, {
              params: {
                gid: movieDetail.gid,
                uc: movieDetail.uc
              }
            });
            
            // 下载封面图片
            let localCoverPath = null;
            if (downloadImages && movieDetail.img) {
              console.log(`正在下载封面图片: ${movie.id}`);
              localCoverPath = await downloadImage(movieDetail.img, 'cover');
            }
            
            // 下载演员图片
            const processedStars = [];
            if (movieDetail.stars && movieDetail.stars.length > 0) {
              for (const star of movieDetail.stars) {
                // 验证女优名字是否有效
                if (!isValidStarName(star.name)) {
                  console.log(`跳过无效女优名字: "${star.name}" (ID: ${star.id})`);
                  continue;
                }
                
                console.log(`正在处理女优: ${star.name} (ID: ${star.id})`);
                
                try {
                  // 调用演员详情API获取头像信息
                  const starDetailResponse = await axios.get(`${JAVBUS_API_URL}/stars/${star.id}`);
                  const starDetail = starDetailResponse.data;
                  
                  console.log(`获取到女优详情: ${starDetail.name}, 头像: ${starDetail.avatar}`);
                  
                  let localStarPath = null;
                  if (downloadImages && starDetail.avatar) {
                    console.log(`正在下载女优头像: ${star.name}`);
                    localStarPath = await downloadImage(starDetail.avatar, 'actress');
                  }
                  
                  processedStars.push({
                    id: star.id,
                    name: star.name || starDetail.name,
                    image: localStarPath,
                    localImage: localStarPath,
                    // 添加详细个人信息
                    birthday: starDetail.birthday,
                    age: starDetail.age,
                    height: starDetail.height,
                    birthplace: starDetail.birthplace,
                    debut_date: starDetail.debut_date,
                    hobby: starDetail.hobby,
                    bust: starDetail.bust,
                    waist: starDetail.waistline,
                    hip: starDetail.hipline,
                    cup_size: starDetail.cup_size,
                    measurements: starDetail.measurements,
                    description: starDetail.description
                  });
                } catch (starError) {
                  console.error(`获取女优详情失败: ${star.name || star.id}`, starError.message);
                  processedStars.push({
                    id: star.id,
                    name: star.name,
                    image: null,
                    localImage: null
                  });
                }
              }
            }
            
            // 处理样品图片
            const processedSamples = [];
            if (movieDetail.samples && movieDetail.samples.length > 0) {
              for (const sample of movieDetail.samples) {
                processedSamples.push({
                  id: sample.id || `sample_${movie.id}_${processedSamples.length + 1}`,
                  alt: sample.alt || `Sample ${processedSamples.length + 1}`
                });
              }
            }
            
            // 转换成我们需要的格式
            // 智能日期选择：比较JAVBUS发行日期和磁力链接日期，选择较早的
            let calculatedReleaseDate = movieDetail.date; // 默认使用JAVBUS API返回的发行日期
            
            if (magnetsResponse.data && magnetsResponse.data.length > 0) {
              let earliestMagnetDate = null;
              
              // 找到最早的磁力链接分享日期
              for (const magnet of magnetsResponse.data) {
                if (magnet.shareDate) {
                  const shareDate = parseShareDate(magnet.shareDate);
                  if (shareDate) {
                    if (!earliestMagnetDate || shareDate < earliestMagnetDate) {
                      earliestMagnetDate = shareDate;
                    }
                  }
                }
              }
              
              // 如果找到有效的磁力分享日期，比较两个日期并选择较早的
              if (earliestMagnetDate && movieDetail.date) {
                const originalReleaseDate = parseShareDate(movieDetail.date);
                
                if (originalReleaseDate) {
                  // 比较两个日期，使用较早的那个
                  if (earliestMagnetDate < originalReleaseDate) {
                    calculatedReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
                    console.log(`📅 影片 ${movie.id}: 磁力日期(${calculatedReleaseDate})早于发行日期(${movieDetail.date})，使用磁力日期`);
                  } else {
                    calculatedReleaseDate = movieDetail.date;
                    console.log(`📅 影片 ${movie.id}: 发行日期(${movieDetail.date})早于等于磁力日期(${earliestMagnetDate.toISOString().split('T')[0]})，使用发行日期`);
                  }
                } else {
                  // 如果原始发行日期无法解析，使用磁力日期
                  calculatedReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
                  console.log(`📅 影片 ${movie.id}: 发行日期无法解析，使用磁力日期: ${calculatedReleaseDate}`);
                }
              } else if (earliestMagnetDate && !movieDetail.date) {
                // 如果没有原始发行日期，使用磁力日期
                calculatedReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
                console.log(`📅 影片 ${movie.id}: 无原始发行日期，使用磁力日期: ${calculatedReleaseDate}`);
              } else {
                // 使用原始发行日期
                console.log(`📅 影片 ${movie.id}: 使用原始发行日期: ${calculatedReleaseDate}`);
              }
            }

            const processedMovie = {
              id: movie.id,
              title: movieDetail.title,
              imageUrl: localCoverPath,
              coverImage: localCoverPath,
              localCoverImage: localCoverPath,
              releaseDate: calculatedReleaseDate,
              duration: movieDetail.videoLength ? `${movieDetail.videoLength}分钟` : '未知',
              description: `${movieDetail.title} - ${movie.id}`,
              // 添加导演信息
              director: movieDetail.director ? {
                id: movieDetail.director.id,
                name: movieDetail.director.name
              } : null,
              // 添加制作商信息
              producer: movieDetail.producer ? {
                id: movieDetail.producer.id,
                name: movieDetail.producer.name
              } : null,
              // 添加发行商信息
              publisher: movieDetail.publisher ? {
                id: movieDetail.publisher.id,
                name: movieDetail.publisher.name
              } : null,
              // 添加系列信息
              series: movieDetail.series ? {
                id: movieDetail.series.id,
                name: movieDetail.series.name
              } : null,
              stars: processedStars,
              genres: movieDetail.genres ? movieDetail.genres.map(genre => genre.name) : [],
              // 不再添加样品图片
              samples: [],
              // 添加相似影片
              similarMovies: movieDetail.similarMovies ? movieDetail.similarMovies.map(similar => ({
                id: similar.id,
                title: similar.title,
                imageUrl: null // 不保存外部URL
              })) : [],
              magnets: magnetsResponse.data ? magnetsResponse.data.map(magnet => ({
                id: magnet.id,
                link: magnet.link,
                title: magnet.title,
                size: magnet.size,
                isHD: magnet.isHD,
                hasSubtitle: magnet.hasSubtitle,
                shareDate: magnet.shareDate
              })) : []
            };
            
            // 立即保存到数据库
            const saveResult = await saveSingleMovieToDatabase(processedMovie);
            
            if (saveResult.isUncensored) {
              uncensoredCount++;
            } else {
              censoredCount++;
            }
            
            importedMovies.push(processedMovie);
            console.log(`✅ 成功处理影片: ${movie.id}`);
            
          } catch (detailError) {
            console.error(`❌ 获取影片详情失败: ${movie.id}`, detailError.message);
          }
        }
        
        console.log(`第${currentPage}页处理完成，当前总计：${importedMovies.length}部影片`);
        
        // 添加页面间的延迟，避免请求过快
        if (currentPage < maxPages) {
          console.log(`⏳ 等待1秒后继续第${currentPage + 1}页... (进度: ${Math.round((currentPage / maxPages) * 100)}%)`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // 减少到1秒延迟
        }
        
      } catch (pageError) {
        console.error(`获取第${currentPage}页数据失败:`, pageError.message);
        // 继续处理下一页
        continue;
      }
    }
    
    // 3. 保存到JSON文件
    const dataDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // 保存最新影片
    fs.writeFileSync(
      path.join(dataDir, 'javbus_movies.json'), 
      JSON.stringify(importedMovies, null, 2)
    );
    
    // 4. 更新首页展示数据
    updateHomePageData(importedMovies);
    
    // 5. 更新分类计数
    const countClient = await pool.connect();
    try {
      await countClient.query(`UPDATE categories SET count = (
        SELECT COUNT(*) FROM video_categories WHERE category_id = 83
      ) WHERE id = 83`); // 更新有码计数
      
      await countClient.query(`UPDATE categories SET count = (
        SELECT COUNT(*) FROM video_categories WHERE category_id = 77
      ) WHERE id = 77`); // 更新无码计数
    } finally {
      countClient.release();
    }
    
    console.log('\n===== 📊 采集完成统计 =====');
    console.log(`📄 采集页数: 第1页 → 第${maxPages}页`);
    console.log(`🎬 获取影片: ${totalMoviesFetched} 部`);
    console.log(`✅ 成功导入: ${importedMovies.length} 部`);
    console.log(`🔰 导入有码影片: ${censoredCount} 部`);
    console.log(`🔞 导入无码影片: ${uncensoredCount} 部`);
    console.log('=============================');

    // 🚀 自动启动视频处理队列
    if (importedMovies.length > 0) {
      try {
        console.log('🎬 开始自动启动视频处理队列...');
        const MovieProcessingService = require('../services/MovieProcessingService');
        const movieProcessingService = new MovieProcessingService();

        // 获取刚导入的影片ID列表（只包含有磁力链接的影片）
        const movieIds = [];
        for (const movie of importedMovies) {
          // 从数据库获取影片的内部ID和磁力链接数量
          const movieResult = await pool.query(
            `SELECT m.id, COUNT(mag.id) as magnet_count
             FROM movies m
             LEFT JOIN magnets mag ON m.id = mag.movie_id
             WHERE m.movie_id = $1
             GROUP BY m.id`,
            [movie.id]
          );
          if (movieResult.rows.length > 0) {
            const movieData = movieResult.rows[0];
            if (movieData.magnet_count > 0) {
              movieIds.push(movieData.id);
              console.log(`✅ 影片 ${movie.id} 有 ${movieData.magnet_count} 个磁力链接，加入处理队列`);
            } else {
              console.log(`⚠️ 影片 ${movie.id} 没有磁力链接，跳过自动处理`);
            }
          }
        }

        if (movieIds.length > 0) {
          // 配置视频处理参数
          const processingConfig = {
            priority: 5,
            watermark: {
              enabled: true,
              text: "JAVFLIX.TV",
              position: "bottom-right",
              opacity: 0.8
            },
            slice: {
              qualities: [
                { resolution: "1080p", bitrate: "5000k", scale: "1920:1080" },
                { resolution: "720p", bitrate: "3000k", scale: "1280:720" }
              ],
              segmentDuration: 6, // 使用6秒分段
              generateThumbnails: true
            }
          };

          // 启动真实的视频处理（同步执行，确保不被中断）
          try {
            const processingResults = await movieProcessingService.startRealProcessing(movieIds, processingConfig);
            const successCount = processingResults.filter(r => r.success).length;
            const failCount = processingResults.length - successCount;
            console.log(`🎉 自动视频处理启动完成: 成功${successCount}个，失败${failCount}个`);
          } catch (autoProcessError) {
            console.error('❌ 自动启动视频处理失败:', autoProcessError);
          }

          console.log(`📋 已安排${movieIds.length}部影片进入视频处理队列`);
        } else {
          console.log('⚠️ 没有找到可处理的影片ID');
        }
      } catch (autoProcessError) {
        console.error('❌ 准备自动视频处理失败:', autoProcessError);
        // 不影响导入流程，只记录错误
      }
    }

    return apiResponse.success(res, {
      importedMovies,
      total: importedMovies.length,
      totalFetched: totalMoviesFetched,
      pagesProcessed: maxPages,
      censored: censoredCount,
      uncensored: uncensoredCount,
      message: `成功从第1页到第${maxPages}页采集并导入${importedMovies.length}部影片，已自动安排视频处理`
    });
  } catch (error) {
    console.error('导入JavBus数据失败:', error);
    return apiResponse.error(res, '导入JavBus数据失败', 500, error);
  }
};

/**
 * 更新首页展示数据
 */
function updateHomePageData(movies) {
  const dataDir = path.join(__dirname, '../../data');
  
  // 确保目录存在
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // 更新最新影片
  fs.writeFileSync(
    path.join(dataDir, 'recent_videos.json'), 
    JSON.stringify(movies.slice(0, 10), null, 2)
  );
  
  // 更新热门影片（按照磁力链接数量排序）
  const popularMovies = [...movies]
    .sort((a, b) => (b.magnets ? b.magnets.length : 0) - (a.magnets ? a.magnets.length : 0))
    .slice(0, 10);
  
  fs.writeFileSync(
    path.join(dataDir, 'popular_videos.json'), 
    JSON.stringify(popularMovies, null, 2)
  );
  
  // 更新推荐影片（随机选择）
  const shuffled = [...movies].sort(() => 0.5 - Math.random());
  
  fs.writeFileSync(
    path.join(dataDir, 'recommended_videos.json'), 
    JSON.stringify(shuffled.slice(0, 6), null, 2)
  );
  
  // 更新特色影片（高清的）
  const featuredMovies = movies
    .filter(movie => movie.magnets && movie.magnets.some(m => m.isHD))
    .slice(0, 4);
  
  fs.writeFileSync(
    path.join(dataDir, 'featured_videos.json'), 
    JSON.stringify(featuredMovies, null, 2)
  );
}

/**
 * 获取已导入的数据库电影
 */
exports.getImportedMovies = async (req, res) => {
  try {
    const client = await pool.connect();
    const result = await client.query(`
      SELECT 
        m.id, 
        m.movie_id, 
        m.title, 
        m.image_url, 
        m.cover_image, 
        m.release_date, 
        m.duration, 
        m.description,
        COUNT(DISTINCT mg.genre_id) AS genre_count,
        COUNT(DISTINCT ms.star_id) AS star_count,
        COUNT(DISTINCT mag.id) AS magnet_count
      FROM 
        movies m
        LEFT JOIN movie_genres mg ON m.id = mg.movie_id
        LEFT JOIN movie_stars ms ON m.id = ms.movie_id
        LEFT JOIN magnets mag ON m.id = mag.movie_id
      GROUP BY 
        m.id
      ORDER BY 
        m.updated_at DESC
    `);
    client.release();
    
    return apiResponse.success(res, { 
      movies: result.rows,
      total: result.rows.length
    });
  } catch (error) {
    console.error('获取导入的电影失败:', error);
    return apiResponse.error(res, '获取导入的电影失败', 500, error);
  }
};

/**
 * 格式化时长（分钟转为hh:mm:ss）
 */
function formatDuration(minutes) {
  if (!minutes || isNaN(minutes)) return '00:00:00';
  
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:00`;
}

/**
 * 更新发行日期 - 从磁力链接中找到最早的分享日期
 */
exports.updateReleaseDate = async (req, res) => {
  try {
    console.log('🚀 开始更新影片发行日期...');
    
    const client = await pool.connect();
    
    try {
      // 获取所有有磁力链接的影片
      const moviesResult = await client.query(`
        SELECT DISTINCT m.id, m.movie_id, m.title, m.release_date,
               COUNT(mag.id) as magnet_count
        FROM movies m 
        INNER JOIN magnets mag ON m.id = mag.movie_id 
        WHERE mag.share_date IS NOT NULL
        GROUP BY m.id, m.movie_id, m.title, m.release_date
        ORDER BY m.id
      `);
      
      console.log(`📊 找到 ${moviesResult.rows.length} 部有磁力链接的影片`);
      
      let processedCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;
      
      // 处理每部影片
      for (const movie of moviesResult.rows) {
        try {
          // 获取该影片的所有磁力链接分享日期
          const magnetsResult = await client.query(
            'SELECT share_date FROM magnets WHERE movie_id = $1 AND share_date IS NOT NULL ORDER BY share_date',
            [movie.id]
          );
          
          if (magnetsResult.rows.length === 0) {
            skippedCount++;
            continue;
          }
          
          // 找到最早的分享日期
          let earliestDate = null;
          for (const magnet of magnetsResult.rows) {
            const shareDate = parseShareDate(magnet.share_date);
            if (shareDate) {
              if (!earliestDate || shareDate < earliestDate) {
                earliestDate = shareDate;
              }
            }
          }
          
          if (!earliestDate) {
            skippedCount++;
            continue;
          }
          
          const earliestDateStr = earliestDate.toISOString().split('T')[0];
          
          // 检查是否需要更新
          if (movie.release_date === earliestDateStr) {
            skippedCount++;
            continue;
          }
          
          // 更新发行日期
          const updateResult = await client.query(
            'UPDATE movies SET release_date = $1, updated_at = NOW() WHERE id = $2',
            [earliestDateStr, movie.id]
          );
          
          if (updateResult.rowCount > 0) {
            updatedCount++;
            console.log(`✅ 已更新影片 ${movie.movie_id} 的发行日期为: ${earliestDateStr}`);
          } else {
            errorCount++;
          }
          
        } catch (movieError) {
          console.error(`❌ 处理影片 ${movie.movie_id} 时出错:`, movieError.message);
          errorCount++;
        } finally {
          processedCount++;
        }
      }
      
      // 输出统计
      console.log('\n' + '='.repeat(50));
      console.log('🎯 更新完成！统计信息：');
      console.log(`📊 处理影片总数: ${processedCount}`);
      console.log(`✅ 成功更新数量: ${updatedCount}`);
      console.log(`⏭️ 跳过数量: ${skippedCount}`);
      console.log(`❌ 错误数量: ${errorCount}`);
      console.log('='.repeat(50));
      
      return apiResponse.success(res, {
        processed: processedCount,
        updated: updatedCount,
        skipped: skippedCount,
        errors: errorCount
      }, '发行日期更新完成');
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error('❌ 更新发行日期失败:', error);
    return apiResponse.error(res, '更新发行日期失败', 500, error);
  }
};

/**
 * 解析分享日期字符串
 */
function parseShareDate(shareDate) {
  if (!shareDate || typeof shareDate !== 'string') {
    return null;
  }
  
  // 支持多种日期格式
  const dateFormats = [
    /^(\d{4}-\d{2}-\d{2})$/,           // 2025-04-28
    /^(\d{4})\/(\d{2})\/(\d{2})$/,     // 2025/04/28
    /^(\d{2})\/(\d{2})\/(\d{4})$/,     // 28/04/2025
    /^(\d{4})年(\d{1,2})月(\d{1,2})日$/ // 2025年4月28日
  ];
  
  // 标准ISO格式
  if (dateFormats[0].test(shareDate)) {
    const date = new Date(shareDate);
    return isNaN(date.getTime()) ? null : date;
  }
  
  // 其他格式的解析可以在这里添加
  // 简单起见，先尝试直接解析
  const date = new Date(shareDate);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * 保存单部影片数据到数据库
 */
async function saveSingleMovieToDatabase(movie) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // 确保有"有码"和"无码"两个分类
    const CENSORED_CATEGORY_ID = 83; // 有码分类ID
    const UNCENSORED_CATEGORY_ID = 77; // 无码分类ID
    
    // 1. 插入电影基本信息，使用UPSERT模式
    let movieResult;
    try {
      movieResult = await client.query(
        `INSERT INTO movies
         (movie_id, title, image_url, cover_image, cached_image_url, release_date, duration, description, status, processing_priority, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'draft', 5, NOW(), NOW())
         ON CONFLICT (movie_id)
         DO UPDATE SET
            title = EXCLUDED.title,
            image_url = EXCLUDED.image_url,
            cover_image = EXCLUDED.cover_image,
            cached_image_url = EXCLUDED.cached_image_url,
            release_date = EXCLUDED.release_date,
            duration = EXCLUDED.duration,
            description = EXCLUDED.description,
            status = COALESCE(movies.status, 'draft'),
            processing_priority = COALESCE(movies.processing_priority, 5),
            updated_at = NOW()
         RETURNING id`,
        [
          movie.id, 
          movie.title, 
          movie.localCoverImage, // 只使用本地图片路径
          movie.localCoverImage, // 只使用本地图片路径
          movie.localCoverImage, // 只使用本地图片路径
          movie.releaseDate, 
          movie.duration, 
          movie.description
        ]
      );
    } catch (insertError) {
      // 如果ON CONFLICT失败，尝试先检查是否存在，然后更新或插入
      console.log(`电影 ${movie.id} 插入失败，尝试更新: ${insertError.message}`);
      
      const existingMovie = await client.query(
        'SELECT id FROM movies WHERE movie_id = $1',
        [movie.id]
      );
      
      if (existingMovie.rows.length > 0) {
        // 更新现有记录
        await client.query(
          `UPDATE movies SET 
           title = $2, image_url = $3, cover_image = $4, cached_image_url = $5,
           release_date = $6, duration = $7, description = $8, updated_at = NOW()
           WHERE movie_id = $1`,
          [
            movie.id, movie.title, movie.localCoverImage, movie.localCoverImage,
            movie.localCoverImage, movie.releaseDate, movie.duration, movie.description
          ]
        );
        movieResult = { rows: [{ id: existingMovie.rows[0].id }] };
      } else {
        // 插入新记录
        movieResult = await client.query(
          `INSERT INTO movies
           (movie_id, title, image_url, cover_image, cached_image_url, release_date, duration, description, status, processing_priority, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'draft', 5, NOW(), NOW())
           RETURNING id`,
          [
            movie.id, movie.title, movie.localCoverImage, movie.localCoverImage,
            movie.localCoverImage, movie.releaseDate, movie.duration, movie.description
          ]
        );
      }
    }
    
    const movieDbId = movieResult.rows[0].id;
    
    // 判断电影是有码还是无码
    const movieId = movie.id;
    // 常见无码ID格式: n1234, heyzo-1234, xxx-av-12345, k1234, 1pondo-123456_789 等
    const uncensoredPattern = /^(n\d+|heyzo-?\d+|xxx-av-?\d+|k\d+|\d{6}[-_]\d{3}|carib|pondo|gachi|1pon|mura|siro|fc2)/i;
    const isUncensored = uncensoredPattern.test(movieId);
    
    // 创建分类关联
    const categoryId = isUncensored ? UNCENSORED_CATEGORY_ID : CENSORED_CATEGORY_ID;
    
    // 插入电影分类关联 - 使用安全的插入方式
    try {
      await client.query(
        `INSERT INTO video_categories (video_id, category_id) 
         VALUES ($1, $2)
         ON CONFLICT (video_id, category_id) DO NOTHING`,
        [movieDbId, categoryId]
      );
    } catch (categoryError) {
      // 如果约束不存在，先检查是否已存在，然后插入
      const existingCategory = await client.query(
        'SELECT 1 FROM video_categories WHERE video_id = $1 AND category_id = $2',
        [movieDbId, categoryId]
      );
      
      if (existingCategory.rows.length === 0) {
        await client.query(
          'INSERT INTO video_categories (video_id, category_id) VALUES ($1, $2)',
          [movieDbId, categoryId]
        );
      }
    }
    
    console.log(`影片 ${movieId} 已分类为: ${isUncensored ? '无码' : '有码'}`);
    
    // 2. 处理导演信息
    if (movie.director) {
      let directorDbId;
      try {
        // 插入或更新导演
        const directorResult = await client.query(
          `INSERT INTO directors 
           (director_id, name, created_at, updated_at) 
           VALUES ($1, $2, NOW(), NOW())
           ON CONFLICT (director_id) 
           DO UPDATE SET 
              name = EXCLUDED.name,
              updated_at = NOW()
           RETURNING id`,
          [movie.director.id, movie.director.name]
        );
        directorDbId = directorResult.rows[0].id;
      } catch (directorError) {
        // 安全插入导演
        const existingDirector = await client.query(
          'SELECT id FROM directors WHERE director_id = $1',
          [movie.director.id]
        );
        
        if (existingDirector.rows.length > 0) {
          directorDbId = existingDirector.rows[0].id;
          await client.query(
            'UPDATE directors SET name = $2, updated_at = NOW() WHERE director_id = $1',
            [movie.director.id, movie.director.name]
          );
        } else {
          const newDirector = await client.query(
            'INSERT INTO directors (director_id, name, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id',
            [movie.director.id, movie.director.name]
          );
          directorDbId = newDirector.rows[0].id;
        }
      }
      
      // 建立电影与导演的关联
      try {
        await client.query(
          `INSERT INTO movie_directors (movie_id, director_id) 
           VALUES ($1, $2)
           ON CONFLICT (movie_id, director_id) DO NOTHING`,
          [movieDbId, directorDbId]
        );
      } catch (directorRelError) {
        // 安全插入关联
        const existingRel = await client.query(
          'SELECT 1 FROM movie_directors WHERE movie_id = $1 AND director_id = $2',
          [movieDbId, directorDbId]
        );
        
        if (existingRel.rows.length === 0) {
          await client.query(
            'INSERT INTO movie_directors (movie_id, director_id) VALUES ($1, $2)',
            [movieDbId, directorDbId]
          );
        }
      }
      
      // 更新电影表中的导演ID
      await client.query(
        `UPDATE movies SET director_id = $1 WHERE id = $2`,
        [directorDbId, movieDbId]
      );
    }
    
    // 3. 处理制作商信息
    if (movie.producer) {
      let producerDbId;
      try {
        // 插入或更新制作商
        const producerResult = await client.query(
          `INSERT INTO producers 
           (producer_id, name, created_at, updated_at) 
           VALUES ($1, $2, NOW(), NOW())
           ON CONFLICT (producer_id) 
           DO UPDATE SET 
              name = EXCLUDED.name,
              updated_at = NOW()
           RETURNING id`,
          [movie.producer.id, movie.producer.name]
        );
        producerDbId = producerResult.rows[0].id;
      } catch (producerError) {
        // 安全插入制作商
        const existingProducer = await client.query(
          'SELECT id FROM producers WHERE producer_id = $1',
          [movie.producer.id]
        );
        
        if (existingProducer.rows.length > 0) {
          producerDbId = existingProducer.rows[0].id;
          await client.query(
            'UPDATE producers SET name = $2, updated_at = NOW() WHERE producer_id = $1',
            [movie.producer.id, movie.producer.name]
          );
        } else {
          const newProducer = await client.query(
            'INSERT INTO producers (producer_id, name, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id',
            [movie.producer.id, movie.producer.name]
          );
          producerDbId = newProducer.rows[0].id;
        }
      }
      
      // 更新电影的制作商关联
      await client.query(
        `UPDATE movies SET producer_id = $1 WHERE id = $2`,
        [producerDbId, movieDbId]
      );
    }
    
    // 4. 处理发行商信息
    if (movie.publisher) {
      let publisherDbId;
      try {
        // 插入或更新发行商
        const publisherResult = await client.query(
          `INSERT INTO publishers 
           (publisher_id, name, created_at, updated_at) 
           VALUES ($1, $2, NOW(), NOW())
           ON CONFLICT (publisher_id) 
           DO UPDATE SET 
              name = EXCLUDED.name,
              updated_at = NOW()
           RETURNING id`,
          [movie.publisher.id, movie.publisher.name]
        );
        publisherDbId = publisherResult.rows[0].id;
      } catch (publisherError) {
        // 安全插入发行商
        const existingPublisher = await client.query(
          'SELECT id FROM publishers WHERE publisher_id = $1',
          [movie.publisher.id]
        );
        
        if (existingPublisher.rows.length > 0) {
          publisherDbId = existingPublisher.rows[0].id;
          await client.query(
            'UPDATE publishers SET name = $2, updated_at = NOW() WHERE publisher_id = $1',
            [movie.publisher.id, movie.publisher.name]
          );
        } else {
          const newPublisher = await client.query(
            'INSERT INTO publishers (publisher_id, name, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id',
            [movie.publisher.id, movie.publisher.name]
          );
          publisherDbId = newPublisher.rows[0].id;
        }
      }
      
      // 更新电影的发行商关联
      await client.query(
        `UPDATE movies SET publisher_id = $1 WHERE id = $2`,
        [publisherDbId, movieDbId]
      );
    }
    
    // 5. 处理系列信息
    if (movie.series) {
      let seriesDbId;
      try {
        // 插入或更新系列
        const seriesResult = await client.query(
          `INSERT INTO series 
           (series_id, name, created_at, updated_at) 
           VALUES ($1, $2, NOW(), NOW())
           ON CONFLICT (series_id) 
           DO UPDATE SET 
              name = EXCLUDED.name,
              updated_at = NOW()
           RETURNING id`,
          [movie.series.id, movie.series.name]
        );
        seriesDbId = seriesResult.rows[0].id;
      } catch (seriesError) {
        // 安全插入系列
        const existingSeries = await client.query(
          'SELECT id FROM series WHERE series_id = $1',
          [movie.series.id]
        );
        
        if (existingSeries.rows.length > 0) {
          seriesDbId = existingSeries.rows[0].id;
          await client.query(
            'UPDATE series SET name = $2, updated_at = NOW() WHERE series_id = $1',
            [movie.series.id, movie.series.name]
          );
        } else {
          const newSeries = await client.query(
            'INSERT INTO series (series_id, name, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id',
            [movie.series.id, movie.series.name]
          );
          seriesDbId = newSeries.rows[0].id;
        }
      }
      
      // 更新电影的系列关联
      await client.query(
        `UPDATE movies SET series_id = $1 WHERE id = $2`,
        [seriesDbId, movieDbId]
      );
    }
    
    // 6. 处理演员信息
    if (movie.stars && movie.stars.length > 0) {
      // 先清除现有关联
      await client.query('DELETE FROM movie_stars WHERE movie_id = $1', [movieDbId]);
      
      for (const star of movie.stars) {
        let starDbId;
        try {
          // 插入或更新演员（包含详细个人信息）
          const starResult = await client.query(
            `INSERT INTO stars 
             (star_id, name, image_url, cached_image_url, birthday, age, height, birthplace, debut_date, hobby, bust, waist, hip, cup_size, measurements, description, created_at, updated_at) 
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW())
             ON CONFLICT (star_id) 
             DO UPDATE SET 
                name = EXCLUDED.name, 
                image_url = EXCLUDED.image_url,
                cached_image_url = EXCLUDED.cached_image_url,
                birthday = COALESCE(EXCLUDED.birthday, stars.birthday),
                age = COALESCE(EXCLUDED.age, stars.age),
                height = COALESCE(EXCLUDED.height, stars.height),
                birthplace = COALESCE(EXCLUDED.birthplace, stars.birthplace),
                debut_date = COALESCE(EXCLUDED.debut_date, stars.debut_date),
                hobby = COALESCE(EXCLUDED.hobby, stars.hobby),
                bust = COALESCE(EXCLUDED.bust, stars.bust),
                waist = COALESCE(EXCLUDED.waist, stars.waist),
                hip = COALESCE(EXCLUDED.hip, stars.hip),
                cup_size = COALESCE(EXCLUDED.cup_size, stars.cup_size),
                measurements = COALESCE(EXCLUDED.measurements, stars.measurements),
                description = COALESCE(EXCLUDED.description, stars.description),
                updated_at = NOW()
             RETURNING id`,
            [
              star.id, 
              star.name, 
              star.localImage, 
              star.localImage,
              star.birthday,
              star.age ? parseInt(star.age) : null,
              star.height ? parseInt(star.height?.replace?.('cm', '') || star.height) : null,
              star.birthplace,
              star.debut_date,
              star.hobby,
              star.bust,
              star.waist,
              star.hip,
              star.cup_size,
              star.measurements,
              star.description
            ]
          );
          starDbId = starResult.rows[0].id;
        } catch (starError) {
          // 安全插入演员
          const existingStar = await client.query(
            'SELECT id FROM stars WHERE star_id = $1',
            [star.id]
          );
          
          if (existingStar.rows.length > 0) {
            starDbId = existingStar.rows[0].id;
            await client.query(
              `UPDATE stars SET 
               name = $2, image_url = $3, cached_image_url = $4, 
               birthday = COALESCE($5, birthday),
               age = COALESCE($6, age),
               height = COALESCE($7, height),
               birthplace = COALESCE($8, birthplace),
               debut_date = COALESCE($9, debut_date),
               hobby = COALESCE($10, hobby),
               bust = COALESCE($11, bust),
               waist = COALESCE($12, waist),
               hip = COALESCE($13, hip),
               cup_size = COALESCE($14, cup_size),
               measurements = COALESCE($15, measurements),
               description = COALESCE($16, description),
               updated_at = NOW() 
               WHERE star_id = $1`,
              [
                star.id, star.name, star.localImage, star.localImage,
                star.birthday,
                star.age ? parseInt(star.age) : null,
                star.height ? parseInt(star.height?.replace?.('cm', '') || star.height) : null,
                star.birthplace,
                star.debut_date,
                star.hobby,
                star.bust,
                star.waist,
                star.hip,
                star.cup_size,
                star.measurements,
                star.description
              ]
            );
          } else {
            const newStar = await client.query(
              `INSERT INTO stars (star_id, name, image_url, cached_image_url, birthday, age, height, birthplace, debut_date, hobby, bust, waist, hip, cup_size, measurements, description, created_at, updated_at) 
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW()) RETURNING id`,
              [
                star.id, star.name, star.localImage, star.localImage,
                star.birthday,
                star.age ? parseInt(star.age) : null,
                star.height ? parseInt(star.height?.replace?.('cm', '') || star.height) : null,
                star.birthplace,
                star.debut_date,
                star.hobby,
                star.bust,
                star.waist,
                star.hip,
                star.cup_size,
                star.measurements,
                star.description
              ]
            );
            starDbId = newStar.rows[0].id;
          }
        }
        
        // 建立电影与演员的关联
        await client.query(
          `INSERT INTO movie_stars (movie_id, star_id) VALUES ($1, $2)`,
          [movieDbId, starDbId]
        );
      }
    }
    
    // 7. 处理类型信息
    if (movie.genres && movie.genres.length > 0) {
      // 先清除现有关联
      await client.query('DELETE FROM movie_genres WHERE movie_id = $1', [movieDbId]);
      
      for (const genreName of movie.genres) {
        let genreDbId;
        try {
          // 插入或获取类型
          const genreResult = await client.query(
            `INSERT INTO genres 
             (name, created_at, updated_at) 
             VALUES ($1, NOW(), NOW())
             ON CONFLICT (name) 
             DO UPDATE SET 
                updated_at = NOW()
             RETURNING id`,
            [genreName]
          );
          genreDbId = genreResult.rows[0].id;
        } catch (genreError) {
          // 安全插入类型
          const existingGenre = await client.query(
            'SELECT id FROM genres WHERE name = $1',
            [genreName]
          );
          
          if (existingGenre.rows.length > 0) {
            genreDbId = existingGenre.rows[0].id;
            await client.query(
              'UPDATE genres SET updated_at = NOW() WHERE name = $1',
              [genreName]
            );
          } else {
            const newGenre = await client.query(
              'INSERT INTO genres (name, created_at, updated_at) VALUES ($1, NOW(), NOW()) RETURNING id',
              [genreName]
            );
            genreDbId = newGenre.rows[0].id;
          }
        }
        
        // 建立电影与类型的关联
        await client.query(
          `INSERT INTO movie_genres (movie_id, genre_id) VALUES ($1, $2)`,
          [movieDbId, genreDbId]
        );
      }
    }
    
    // 8. 处理相似影片
    if (movie.similarMovies && movie.similarMovies.length > 0) {
      // 先清除现有相似影片
      await client.query('DELETE FROM similar_movies WHERE movie_id = $1', [movieDbId]);
      
      for (const similar of movie.similarMovies) {
        // 对于相似影片，尝试查找是否已经有本地缓存的图片
        const cachedImageResult = await client.query(
          `SELECT cached_image_url FROM movies WHERE movie_id = $1`,
          [similar.id]
        );
        
        // 如果数据库中已有这部电影，并且有本地图片，就使用本地图片
        const cachedImageUrl = cachedImageResult.rows.length > 0 && cachedImageResult.rows[0].cached_image_url
          ? cachedImageResult.rows[0].cached_image_url
          : null;
        
        await client.query(
          `INSERT INTO similar_movies 
           (movie_id, similar_movie_id, similar_title, similar_image, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, NOW(), NOW())`,
          [movieDbId, similar.id, similar.title, cachedImageUrl]
        );
      }
    }
    
    // 10. 处理磁力链接
    if (movie.magnets && movie.magnets.length > 0) {
      // 先清除现有磁力链接
      await client.query('DELETE FROM magnets WHERE movie_id = $1', [movieDbId]);
      
      for (const magnet of movie.magnets) {
        await client.query(
          `INSERT INTO magnets 
           (movie_id, magnet_id, link, title, size, is_hd, has_subtitle, share_date, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())`,
          [
            movieDbId,
            magnet.id || null,
            magnet.link,
            magnet.title || null,
            magnet.size,
            magnet.isHD,
            magnet.hasSubtitle,
            magnet.shareDate || null
          ]
        );
      }
    }
    
    await client.query('COMMIT');
    console.log(`成功保存影片 ${movie.id} 到数据库`);
    return { success: true, movieId: movieDbId, isUncensored };
  } catch (error) {
    await client.query('ROLLBACK');
    console.error(`保存影片 ${movie.id} 到数据库失败:`, error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 处理从后台保存JavBus影片数据
 */
exports.saveMovies = async (req, res) => {
  try {
    const { movies, downloadImages = true } = req.body;
    
    if (!movies || !Array.isArray(movies) || movies.length === 0) {
      return apiResponse.badRequest(res, '请提供有效的影片数据');
    }
    
    console.log(`接收到${movies.length}部影片数据，准备保存到数据库，downloadImages=${downloadImages}`);
    
    // 设置导入状态
    importStatus.isImporting = true;
    importStatus.totalMovies = movies.length;
    importStatus.processedMovies = 0;
    importStatus.successfulMovies = 0;
    importStatus.failedMovies = 0;
    importStatus.startTime = new Date();
    importStatus.endTime = null;
    importStatus.lastError = null;
    
    // 异步处理，立即返回响应
    res.status(202).json({
      success: true,
      message: '开始处理影片数据',
      taskId: importStatus.startTime.getTime(),
      totalMovies: movies.length
    });
    
    // 异步处理
    (async () => {
      try {
        // 确保表存在
        const initClient = await pool.connect();
        await createTablesIfNotExist(initClient);
        initClient.release();
        
        // 处理并保存每部影片数据
        const processedMovies = [];
        let censoredCount = 0;
        let uncensoredCount = 0;
        
        for (const movie of movies) {
          try {
            importStatus.currentMovieId = movie.id;
            
            // 下载封面图片 (如果需要下载图片)
            let localCoverPath = null;
            if (downloadImages && movie.img) {
              console.log(`正在下载封面图片: ${movie.id}`);
              localCoverPath = await downloadImage(movie.img, 'cover');
            }
            
            // 处理演员图片
            const processedStars = [];
            if (movie.stars && movie.stars.length > 0) {
              for (const star of movie.stars) {
                // 验证女优名字是否有效
                if (!isValidStarName(star.name)) {
                  console.log(`跳过无效女优名字: "${star.name}" (ID: ${star.id})`);
                  continue;
                }
                
                console.log(`正在处理女优: ${star.name} (ID: ${star.id})`);
                
                try {
                  // 调用演员详情API获取头像信息
                  const starDetailResponse = await axios.get(`${JAVBUS_API_URL}/stars/${star.id}`);
                  const starDetail = starDetailResponse.data;
                  
                  console.log(`获取到女优详情: ${starDetail.name}, 头像: ${starDetail.avatar}`);
                  
                  let localStarPath = null;
                  if (downloadImages && starDetail.avatar) {
                    console.log(`正在下载女优头像: ${star.name}`);
                    localStarPath = await downloadImage(starDetail.avatar, 'actress');
                  }
                  
                  processedStars.push({
                    id: star.id,
                    name: star.name || starDetail.name,
                    image: localStarPath,
                    localImage: localStarPath,
                    // 添加详细个人信息
                    birthday: starDetail.birthday,
                    age: starDetail.age,
                    height: starDetail.height,
                    birthplace: starDetail.birthplace,
                    debut_date: starDetail.debut_date,
                    hobby: starDetail.hobby,
                    bust: starDetail.bust,
                    waist: starDetail.waistline,
                    hip: starDetail.hipline,
                    cup_size: starDetail.cup_size,
                    measurements: starDetail.measurements,
                    description: starDetail.description
                  });
                } catch (starError) {
                  console.error(`获取女优详情失败: ${star.name || star.id}`, starError.message);
                  processedStars.push({
                    id: star.id,
                    name: star.name,
                    image: null,
                    localImage: null
                  });
                }
              }
            }
            
            // 处理相似影片
            const processedSimilarMovies = [];
            if (movie.similarMovies && movie.similarMovies.length > 0) {
              for (const similar of movie.similarMovies) {
                processedSimilarMovies.push({
                  id: similar.id,
                  title: similar.title,
                  imageUrl: null // 不保存外部URL
                });
              }
            }
            
            // 智能日期选择：比较JAVBUS发行日期和磁力链接日期，选择较早的
            let calculatedReleaseDate = movie.date; // 默认使用JAVBUS API返回的发行日期
            
            if (movie.magnets && movie.magnets.length > 0) {
              let earliestMagnetDate = null;
              
              // 找到最早的磁力链接分享日期
              for (const magnet of movie.magnets) {
                if (magnet.shareDate) {
                  const shareDate = parseShareDate(magnet.shareDate);
                  if (shareDate) {
                    if (!earliestMagnetDate || shareDate < earliestMagnetDate) {
                      earliestMagnetDate = shareDate;
                    }
                  }
                }
              }
              
              // 如果找到有效的磁力分享日期，比较两个日期并选择较早的
              if (earliestMagnetDate && movie.date) {
                const originalReleaseDate = parseShareDate(movie.date);
                
                if (originalReleaseDate) {
                  // 比较两个日期，使用较早的那个
                  if (earliestMagnetDate < originalReleaseDate) {
                    calculatedReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
                    console.log(`📅 影片 ${movie.id}: 磁力日期(${calculatedReleaseDate})早于发行日期(${movie.date})，使用磁力日期`);
                  } else {
                    calculatedReleaseDate = movie.date;
                    console.log(`📅 影片 ${movie.id}: 发行日期(${movie.date})早于等于磁力日期(${earliestMagnetDate.toISOString().split('T')[0]})，使用发行日期`);
                  }
                } else {
                  // 如果原始发行日期无法解析，使用磁力日期
                  calculatedReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
                  console.log(`📅 影片 ${movie.id}: 发行日期无法解析，使用磁力日期: ${calculatedReleaseDate}`);
                }
              } else if (earliestMagnetDate && !movie.date) {
                // 如果没有原始发行日期，使用磁力日期
                calculatedReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
                console.log(`📅 影片 ${movie.id}: 无原始发行日期，使用磁力日期: ${calculatedReleaseDate}`);
              } else {
                // 使用原始发行日期
                console.log(`📅 影片 ${movie.id}: 使用原始发行日期: ${calculatedReleaseDate}`);
              }
            }

            const processedMovie = {
              id: movie.id,
              title: movie.title,
              imageUrl: downloadImages ? localCoverPath : null, // 下载图片时使用本地路径，否则为null
              coverImage: downloadImages ? localCoverPath : null, // 下载图片时使用本地路径，否则为null
              localImageUrl: localCoverPath,
              localCoverImage: localCoverPath,
              releaseDate: calculatedReleaseDate,
              duration: movie.length || '未知',
              description: `${movie.title} - ${movie.id}`,
              // 添加导演信息
              director: movie.director ? {
                id: movie.director.id,
                name: movie.director.name
              } : null,
              // 添加制作商信息
              producer: movie.producer ? {
                id: movie.producer.id,
                name: movie.producer.name
              } : null,
              // 添加发行商信息
              publisher: movie.publisher ? {
                id: movie.publisher.id,
                name: movie.publisher.name
              } : null,
              // 添加系列信息
              series: movie.series ? {
                id: movie.series.id,
                name: movie.series.name
              } : null,
              stars: processedStars,
              genres: movie.genres ? movie.genres.map(g => g.name) : (movie.tags || []),
              // 不再添加样品图片
              samples: [],
              // 添加相似影片
              similarMovies: processedSimilarMovies,
              magnets: movie.magnets ? movie.magnets.map(magnet => ({
                id: magnet.id,
                link: magnet.link,
                title: magnet.title,
                size: magnet.size,
                isHD: magnet.isHD,
                hasSubtitle: magnet.hasSubtitle,
                shareDate: magnet.shareDate
              })) : []
            };
            
            // 立即保存到数据库
            const saveResult = await saveSingleMovieToDatabase(processedMovie);
            
            if (saveResult.isUncensored) {
              uncensoredCount++;
            } else {
              censoredCount++;
            }
            
            processedMovies.push(processedMovie);
            importStatus.successfulMovies++;
          } catch (movieError) {
            console.error(`处理影片失败: ${movie.id}`, movieError);
            importStatus.failedMovies++;
            importStatus.lastError = movieError.message;
          } finally {
            importStatus.processedMovies++;
          }
        }
        
        // 更新首页展示数据
        updateHomePageData(processedMovies);
        
        // 更新分类计数
        const countClient = await pool.connect();
        try {
          await countClient.query(`UPDATE categories SET count = (
            SELECT COUNT(*) FROM video_categories WHERE category_id = 83
          ) WHERE id = 83`); // 更新有码计数
          
          await countClient.query(`UPDATE categories SET count = (
            SELECT COUNT(*) FROM video_categories WHERE category_id = 77
          ) WHERE id = 77`); // 更新无码计数
        } finally {
          countClient.release();
        }
        
        console.log('===== 分类统计 =====');
        console.log(`导入有码影片: ${censoredCount} 部`);
        console.log(`导入无码影片: ${uncensoredCount} 部`);
        console.log('===================');

        console.log(`成功处理 ${processedMovies.length} 部影片`);

        // 🚀 自动启动视频处理队列
        if (processedMovies.length > 0) {
          try {
            console.log('🎬 开始自动启动视频处理队列...');
            const MovieProcessingService = require('../services/MovieProcessingService');
            const movieProcessingService = new MovieProcessingService();

            // 获取刚导入的影片ID列表（只包含有磁力链接的影片）
            const movieIds = [];
            for (const movie of processedMovies) {
              // 从数据库获取影片的内部ID和磁力链接数量
              const movieResult = await pool.query(
                `SELECT m.id, COUNT(mag.id) as magnet_count
                 FROM movies m
                 LEFT JOIN magnets mag ON m.id = mag.movie_id
                 WHERE m.movie_id = $1
                 GROUP BY m.id`,
                [movie.id]
              );
              if (movieResult.rows.length > 0) {
                const movieData = movieResult.rows[0];
                if (movieData.magnet_count > 0) {
                  movieIds.push(movieData.id);
                  console.log(`✅ 影片 ${movie.id} 有 ${movieData.magnet_count} 个磁力链接，加入处理队列`);
                } else {
                  console.log(`⚠️ 影片 ${movie.id} 没有磁力链接，跳过自动处理`);
                }
              }
            }

            if (movieIds.length > 0) {
              // 配置视频处理参数
              const processingConfig = {
                priority: 5,
                watermark: {
                  enabled: true,
                  text: "JAVFLIX.TV",
                  position: "bottom-right",
                  opacity: 0.8
                },
                slice: {
                  qualities: [
                    { resolution: "1080p", bitrate: "5000k", scale: "1920:1080" },
                    { resolution: "720p", bitrate: "3000k", scale: "1280:720" }
                  ],
                  segmentDuration: 6, // 使用6秒分段
                  generateThumbnails: true
                }
              };

              // 启动真实的视频处理
              const processingResults = await movieProcessingService.startRealProcessing(movieIds, processingConfig);

              const successCount = processingResults.filter(r => r.success).length;
              const failCount = processingResults.length - successCount;

              console.log(`🎉 自动视频处理启动完成: 成功${successCount}个，失败${failCount}个`);
            } else {
              console.log('⚠️ 没有找到可处理的影片ID');
            }
          } catch (autoProcessError) {
            console.error('❌ 自动启动视频处理失败:', autoProcessError);
            // 不影响导入流程，只记录错误
          }
        }
      } catch (error) {
        console.error('异步处理影片数据失败:', error);
        importStatus.lastError = error.message;
      } finally {
        importStatus.isImporting = false;
        importStatus.endTime = new Date();
        importStatus.currentMovieId = null;
      }
    })();
  } catch (error) {
    console.error('保存影片数据失败:', error);
    return apiResponse.error(res, '保存影片数据失败', 500, error);
  }
};

/**
 * 获取导入状态
 */
exports.getImportStatus = async (req, res) => {
  const status = {
    ...importStatus,
    progress: importStatus.totalMovies > 0 
      ? Math.round((importStatus.processedMovies / importStatus.totalMovies) * 100) 
      : 0,
    duration: importStatus.startTime && (importStatus.endTime || new Date()) - importStatus.startTime,
  };
  
  return apiResponse.success(res, status);
};

/**
 * 下载影片相关图片到本地
 */
exports.downloadMovieImages = async (req, res) => {
  try {
    const { movieId, coverImage, sampleImages, starImages } = req.body;
    
    if (!movieId) {
      return apiResponse.badRequest(res, '请提供影片ID');
    }
    
    console.log(`开始下载影片 ${movieId} 的图片资源`);
    
    // 下载结果
    const result = {
      movieId,
      coverPath: null,
      starPaths: [],
      samplePaths: []
    };
    
    // 下载封面图片
    if (coverImage) {
      console.log(`下载影片 ${movieId} 的封面图片`);
      result.coverPath = await downloadImage(coverImage, 'cover');
    }
    
    // 下载演员图片
    if (starImages && Array.isArray(starImages)) {
      for (const star of starImages) {
        if (star.image) {
          const starPath = await downloadImage(star.image, 'actress');
          if (starPath) {
            result.starPaths.push({
              id: star.id,
              path: starPath
            });
          }
        }
      }
    }
    
    // 下载样品图片
    if (sampleImages && Array.isArray(sampleImages)) {
      for (const sampleUrl of sampleImages) {
        const samplePath = await downloadImage(sampleUrl, 'sample');
        if (samplePath) {
          result.samplePaths.push(samplePath);
        }
      }
    }
    
    return apiResponse.success(res, {
      movieId,
      result,
      message: '图片下载完成'
    });
  } catch (error) {
    console.error('下载影片图片失败:', error);
    return apiResponse.error(res, '下载影片图片失败', 500, error);
  }
};

/**
 * 确保需要的表结构存在
 */
async function createTablesIfNotExist(client) {
  try {
    // 先创建不依赖其他表的基础表
    
    // 创建演员表
    await client.query(`
      CREATE TABLE IF NOT EXISTS stars (
        id SERIAL PRIMARY KEY,
        star_id VARCHAR(50) NOT NULL,
        name VARCHAR(100) NOT NULL,
        image_url TEXT,
        cached_image_url TEXT,
        birthday VARCHAR(50),
        age INTEGER,
        height INTEGER,
        birthplace VARCHAR(100),
        debut_date VARCHAR(50),
        hobby TEXT,
        bust VARCHAR(20),
        waist VARCHAR(20),
        hip VARCHAR(20),
        cup_size VARCHAR(10),
        measurements VARCHAR(50),
        description TEXT,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建类型表
    await client.query(`
      CREATE TABLE IF NOT EXISTS genres (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建导演表
    await client.query(`
      CREATE TABLE IF NOT EXISTS directors (
        id SERIAL PRIMARY KEY,
        director_id VARCHAR(50) NOT NULL,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建制作商表
    await client.query(`
      CREATE TABLE IF NOT EXISTS producers (
        id SERIAL PRIMARY KEY,
        producer_id VARCHAR(50) NOT NULL,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建发行商表
    await client.query(`
      CREATE TABLE IF NOT EXISTS publishers (
        id SERIAL PRIMARY KEY,
        publisher_id VARCHAR(50) NOT NULL,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建系列表
    await client.query(`
      CREATE TABLE IF NOT EXISTS series (
        id SERIAL PRIMARY KEY,
        series_id VARCHAR(50) NOT NULL,
        name VARCHAR(200) NOT NULL,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建电影表 (依赖其他表的外键)
    await client.query(`
      CREATE TABLE IF NOT EXISTS movies (
        id SERIAL PRIMARY KEY,
        movie_id VARCHAR(50) NOT NULL,
        title TEXT NOT NULL,
        image_url TEXT,
        cover_image TEXT,
        cached_image_url TEXT,
        release_date VARCHAR(50),
        duration VARCHAR(50),
        description TEXT,
        status VARCHAR(20) DEFAULT 'draft',
        processing_priority INTEGER DEFAULT 5,
        video_urls JSONB,
        processing_started_at TIMESTAMP,
        processing_completed_at TIMESTAMP,
        director_id INTEGER REFERENCES directors(id) ON DELETE SET NULL,
        producer_id INTEGER REFERENCES producers(id) ON DELETE SET NULL,
        publisher_id INTEGER REFERENCES publishers(id) ON DELETE SET NULL,
        series_id INTEGER REFERENCES series(id) ON DELETE SET NULL,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建样品图片表
    await client.query(`
      CREATE TABLE IF NOT EXISTS samples (
        id SERIAL PRIMARY KEY,
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        sample_id VARCHAR(50),        
        alt TEXT,
        src TEXT,
        thumbnail TEXT,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建磁力链接表
    await client.query(`
      CREATE TABLE IF NOT EXISTS magnets (
        id SERIAL PRIMARY KEY,
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        magnet_id VARCHAR(100),
        link TEXT NOT NULL,
        title TEXT,
        size VARCHAR(50),
        is_hd BOOLEAN DEFAULT FALSE,
        has_subtitle BOOLEAN DEFAULT FALSE,
        share_date VARCHAR(50),
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建电影-演员关联表
    await client.query(`
      CREATE TABLE IF NOT EXISTS movie_stars (
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        star_id INTEGER REFERENCES stars(id) ON DELETE CASCADE,
        PRIMARY KEY (movie_id, star_id)
      )
    `);
    
    // 创建电影-类型关联表
    await client.query(`
      CREATE TABLE IF NOT EXISTS movie_genres (
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        genre_id INTEGER REFERENCES genres(id) ON DELETE CASCADE,
        PRIMARY KEY (movie_id, genre_id)
      )
    `);
    
    // 创建相似影片表
    await client.query(`
      CREATE TABLE IF NOT EXISTS similar_movies (
        id SERIAL PRIMARY KEY,
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        similar_movie_id VARCHAR(50) NOT NULL,
        similar_title TEXT,
        similar_image TEXT,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建电影-导演关联表
    await client.query(`
      CREATE TABLE IF NOT EXISTS movie_directors (
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        director_id INTEGER REFERENCES directors(id) ON DELETE CASCADE,
        PRIMARY KEY (movie_id, director_id)
      )
    `);
    
    // 创建分类表 (如果不存在)
    await client.query(`
      CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) NOT NULL,
        count INTEGER DEFAULT 0,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP NOT NULL
      )
    `);
    
    // 创建电影-分类关联表
    await client.query(`
      CREATE TABLE IF NOT EXISTS video_categories (
        video_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
        PRIMARY KEY (video_id, category_id)
      )
    `);

    // 创建视频处理任务表
    await client.query(`
      CREATE TABLE IF NOT EXISTS video_processing_tasks (
        id SERIAL PRIMARY KEY,
        task_uuid VARCHAR(100) NOT NULL UNIQUE,
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        task_type VARCHAR(50) DEFAULT 'video_processing',
        status VARCHAR(50) DEFAULT 'pending',
        progress INTEGER DEFAULT 0,
        priority INTEGER DEFAULT 5,
        magnet_link TEXT,
        watermark_config JSONB,
        slice_config JSONB,
        error_message TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
        started_at TIMESTAMP,
        completed_at TIMESTAMP
      )
    `);
    
    // 检查并创建基本分类数据
    await client.query(`
      INSERT INTO categories (id, name, slug, count, created_at, updated_at)
      VALUES 
        (77, '无码', 'uncensored', 0, NOW(), NOW()),
        (83, '有码', 'censored', 0, NOW(), NOW())
      ON CONFLICT (id) DO NOTHING
    `);
    
    console.log('数据库表创建完成，开始检查约束...');
    
    // 安全地添加唯一约束
    const constraints = [
      { table: 'stars', column: 'star_id', name: 'stars_star_id_key' },
      { table: 'genres', column: 'name', name: 'genres_name_key' },
      { table: 'directors', column: 'director_id', name: 'directors_director_id_key' },
      { table: 'producers', column: 'producer_id', name: 'producers_producer_id_key' },
      { table: 'publishers', column: 'publisher_id', name: 'publishers_publisher_id_key' },
      { table: 'series', column: 'series_id', name: 'series_series_id_key' },
      { table: 'movies', column: 'movie_id', name: 'movies_movie_id_key' },
      { table: 'categories', column: 'name', name: 'categories_name_key' },
      { table: 'categories', column: 'slug', name: 'categories_slug_key' }
    ];
    
    for (const constraint of constraints) {
      try {
        await client.query(`
          DO $$
          BEGIN
            IF NOT EXISTS (
              SELECT 1 FROM pg_constraint 
              WHERE conname = '${constraint.name}' 
              AND conrelid = '${constraint.table}'::regclass
            ) THEN
              ALTER TABLE ${constraint.table} ADD CONSTRAINT ${constraint.name} UNIQUE (${constraint.column});
            END IF;
          END $$;
        `);
        console.log(`✅ 约束 ${constraint.name} 检查完成`);
      } catch (constraintError) {
        console.log(`⚠️  约束 ${constraint.name} 已存在或创建失败，跳过: ${constraintError.message}`);
      }
    }
    
    console.log('数据库表和约束检查完成');
    
    // 检查是否需要添加缺失的列
    try {
      // 检查movies表是否有缺失的列
      const movieTableColumns = ['director_id', 'producer_id', 'publisher_id', 'series_id', 'status', 'processing_priority', 'video_urls', 'processing_started_at', 'processing_completed_at'];
      for (const column of movieTableColumns) {
        const columnResult = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'movies' AND column_name = $1
        `, [column]);

        if (columnResult.rows.length === 0) {
          console.log(`添加 ${column} 列到 movies 表`);
          if (column === 'status') {
            await client.query(`ALTER TABLE movies ADD COLUMN ${column} VARCHAR(20) DEFAULT 'draft'`);
          } else if (column === 'processing_priority') {
            await client.query(`ALTER TABLE movies ADD COLUMN ${column} INTEGER DEFAULT 5`);
          } else if (column === 'video_urls') {
            await client.query(`ALTER TABLE movies ADD COLUMN ${column} JSONB`);
          } else if (column === 'processing_started_at' || column === 'processing_completed_at') {
            await client.query(`ALTER TABLE movies ADD COLUMN ${column} TIMESTAMP`);
          } else {
            await client.query(`ALTER TABLE movies ADD COLUMN ${column} INTEGER REFERENCES ${column.replace('_id', '')}s(id) ON DELETE SET NULL`);
          }
        }
      }

      // 检查stars表是否有缺失的列
      const starTableColumns = ['birthday', 'age', 'height', 'birthplace', 'debut_date', 'hobby', 'bust', 'waist', 'hip', 'cup_size', 'measurements', 'description'];
      for (const column of starTableColumns) {
        const columnResult = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'stars' AND column_name = $1
        `, [column]);

        if (columnResult.rows.length === 0) {
          console.log(`添加 ${column} 列到 stars 表`);
          let dataType = 'TEXT';
          if (column === 'age' || column === 'height') {
            dataType = 'INTEGER';
          } else if (['birthday', 'debut_date', 'birthplace'].includes(column)) {
            dataType = 'VARCHAR(100)';
          } else if (['bust', 'waist', 'hip', 'cup_size'].includes(column)) {
            dataType = 'VARCHAR(20)';
          } else if (column === 'measurements') {
            dataType = 'VARCHAR(50)';
          }
          await client.query(`ALTER TABLE stars ADD COLUMN ${column} ${dataType}`);
        }
      }

      // 检查magnets表是否有缺失的列
      const magnetTableColumns = ['magnet_id', 'title', 'share_date'];
      for (const column of magnetTableColumns) {
        const columnResult = await client.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'magnets' AND column_name = $1
        `, [column]);

        if (columnResult.rows.length === 0) {
          console.log(`添加 ${column} 列到 magnets 表`);
          const dataType = column === 'magnet_id' ? 'VARCHAR(100)' : 'TEXT';
          await client.query(`ALTER TABLE magnets ADD COLUMN ${column} ${dataType}`);
        }
      }
    } catch (error) {
      console.error('检查或添加列失败:', error);
    }
    
  } catch (error) {
    console.error('创建数据库表时出错:', error);
    throw error;
  }
}

/**
 * 获取详细的导入统计信息 - 用于大规模采集监控
 */
exports.getImportStats = async (req, res) => {
  try {
    const client = await pool.connect();
    
    // 获取基本统计
    const stats = {};
    
    // 影片总数
    const movieCount = await client.query('SELECT COUNT(*) as count FROM movies');
    stats.totalMovies = parseInt(movieCount.rows[0].count);
    
    // 女优总数
    const starCount = await client.query('SELECT COUNT(*) as count FROM stars');
    stats.totalStars = parseInt(starCount.rows[0].count);
    
    // 磁力链接总数
    const magnetCount = await client.query('SELECT COUNT(*) as count FROM magnets');
    stats.totalMagnets = parseInt(magnetCount.rows[0].count);
    
    // 有码/无码分布
    const categoryStats = await client.query(`
      SELECT 
        c.name as category_name,
        COUNT(vc.video_id) as count
      FROM categories c
      LEFT JOIN video_categories vc ON c.id = vc.category_id
      WHERE c.id IN (77, 83)
      GROUP BY c.id, c.name
    `);
    
    stats.categoryDistribution = {};
    categoryStats.rows.forEach(row => {
      stats.categoryDistribution[row.category_name] = parseInt(row.count);
    });
    
    // 最近24小时导入的影片数
    const recentImports = await client.query(`
      SELECT COUNT(*) as count 
      FROM movies 
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `);
    stats.recentImports24h = parseInt(recentImports.rows[0].count);
    
    // 最近导入的10部影片
    const recentMovies = await client.query(`
      SELECT movie_id, title, created_at 
      FROM movies 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    stats.recentMovies = recentMovies.rows;
    
    // 磁力链接分布统计
    const magnetStats = await client.query(`
      SELECT 
        COUNT(CASE WHEN is_hd = true THEN 1 END) as hd_count,
        COUNT(CASE WHEN has_subtitle = true THEN 1 END) as subtitle_count,
        COUNT(*) as total_magnets
      FROM magnets
    `);
    
    if (magnetStats.rows.length > 0) {
      const magnetRow = magnetStats.rows[0];
      stats.magnetStats = {
        total: parseInt(magnetRow.total_magnets),
        hdCount: parseInt(magnetRow.hd_count || 0),
        subtitleCount: parseInt(magnetRow.subtitle_count || 0)
      };
    }
    
    // 数据库大小估算
    const dbSize = await client.query(`
      SELECT 
        pg_size_pretty(pg_total_relation_size('movies')) as movies_size,
        pg_size_pretty(pg_total_relation_size('stars')) as stars_size,
        pg_size_pretty(pg_total_relation_size('magnets')) as magnets_size
    `);
    
    if (dbSize.rows.length > 0) {
      stats.databaseSizes = dbSize.rows[0];
    }
    
    client.release();
    
    return apiResponse.success(res, {
      stats,
      timestamp: new Date().toISOString(),
      message: '统计信息获取成功'
    });
    
  } catch (error) {
    console.error('获取导入统计失败:', error);
    return apiResponse.error(res, '获取导入统计失败', 500, error);
  }
};

/**
 * 添加自定义磁力下载任务
 */
exports.addCustomMagnetDownload = async (req, res) => {
  try {
    const { magnetLink, title, movieId, priority = 'medium', autoStart = true, remark } = req.body;

    // 验证必填字段
    if (!magnetLink || !title) {
      return apiResponse.badRequest(res, '磁力链接和标题不能为空');
    }

    // 验证磁力链接格式
    if (!magnetLink.startsWith('magnet:?xt=urn:btih:')) {
      return apiResponse.badRequest(res, '无效的磁力链接格式');
    }

    console.log('添加自定义磁力下载任务:', { magnetLink, title, movieId, priority, autoStart });

    // 创建下载任务记录
    const Download = require('../models/download.model');

    const downloadTask = new Download({
      movieId: movieId || null,
      title,
      magnetLink,
      magnetInfo: {
        link: magnetLink,
        hasSubtitle: false,
        isHD: false,
        size: '未知'
      },
      status: autoStart ? 'waiting' : 'stopped',
      priority,
      autoStart,
      remark,
      createdBy: req.user ? req.user.id : null
    });

    await downloadTask.save();

    // 如果设置为自动开始，调用视频处理服务
    if (autoStart) {
      try {
        const MovieProcessingService = require('../services/MovieProcessingService');
        const movieProcessingService = new MovieProcessingService();

        // 创建一个临时的影片对象用于处理
        const tempMovie = {
          id: movieId || `custom_${Date.now()}`,
          title: title,
          status: 'pending'
        };

        // 创建视频处理任务
        const taskConfig = {
          priority: priority === 'high' ? 8 : priority === 'medium' ? 5 : 3,
          watermark: {
            enabled: true,
            text: "JAVFLIX.TV",
            position: "bottom-right",
            opacity: 0.8
          },
          slice: {
            qualities: [
              { resolution: "1080p", bitrate: "5000k" },
              { resolution: "720p", bitrate: "3000k" },
              { resolution: "480p", bitrate: "1500k" }
            ],
            segmentDuration: 10,
            generateThumbnails: true
          }
        };

        const task = await movieProcessingService.createVideoTaskWithMagnet(
          tempMovie.id,
          magnetLink,
          taskConfig
        );

        // 更新下载任务状态
        downloadTask.status = 'downloading';
        downloadTask.logs.push({
          time: new Date().toISOString(),
          message: `视频处理任务已创建: ${task.task_uuid}`
        });
        await downloadTask.save();

        console.log(`自定义磁力下载任务已创建并开始处理: ${task.task_uuid}`);

      } catch (processingError) {
        console.error('创建视频处理任务失败:', processingError);

        // 更新下载任务状态为失败
        downloadTask.status = 'failed';
        downloadTask.error = processingError.message;
        downloadTask.logs.push({
          time: new Date().toISOString(),
          message: `创建视频处理任务失败: ${processingError.message}`
        });
        await downloadTask.save();
      }
    }

    return apiResponse.success(res, {
      task: downloadTask,
      message: autoStart ? '磁力下载任务已添加并开始处理' : '磁力下载任务已添加'
    });

  } catch (error) {
    console.error('添加自定义磁力下载任务失败:', error);
    return apiResponse.error(res, '添加磁力下载任务失败', 500, error);
  }
};

/**
 * 获取磁力下载任务列表
 */
exports.getMagnetDownloadTasks = async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;

    const Download = require('../models/download.model');

    // 构建查询条件
    const query = {};
    if (status) {
      query.status = status;
    }

    // 分页查询
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const tasks = await Download.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('createdBy', 'username email');

    const total = await Download.countDocuments(query);

    return apiResponse.success(res, {
      tasks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('获取磁力下载任务列表失败:', error);
    return apiResponse.error(res, '获取磁力下载任务列表失败', 500, error);
  }
};

/**
 * 取消磁力下载任务
 */
exports.cancelMagnetDownload = async (req, res) => {
  try {
    const { taskId } = req.params;

    const Download = require('../models/download.model');

    const task = await Download.findById(taskId);
    if (!task) {
      return apiResponse.notFound(res, '下载任务不存在');
    }

    // 只能取消等待中或下载中的任务
    if (!['waiting', 'downloading'].includes(task.status)) {
      return apiResponse.badRequest(res, '只能取消等待中或下载中的任务');
    }

    // 更新任务状态
    task.status = 'stopped';
    task.logs.push({
      time: new Date().toISOString(),
      message: '任务已被用户取消'
    });
    await task.save();

    // TODO: 通知视频处理服务取消任务

    return apiResponse.success(res, {
      message: '下载任务已取消'
    });

  } catch (error) {
    console.error('取消磁力下载任务失败:', error);
    return apiResponse.error(res, '取消磁力下载任务失败', 500, error);
  }
};

/**
 * 重试磁力下载任务
 */
exports.retryMagnetDownload = async (req, res) => {
  try {
    const { taskId } = req.params;

    const Download = require('../models/download.model');

    const task = await Download.findById(taskId);
    if (!task) {
      return apiResponse.notFound(res, '下载任务不存在');
    }

    // 只能重试失败的任务
    if (task.status !== 'failed') {
      return apiResponse.badRequest(res, '只能重试失败的任务');
    }

    // 重置任务状态
    task.status = 'waiting';
    task.progress = 0;
    task.error = null;
    task.logs.push({
      time: new Date().toISOString(),
      message: '任务已重新提交'
    });
    await task.save();

    // TODO: 重新提交到视频处理服务

    return apiResponse.success(res, {
      message: '下载任务已重新提交'
    });

  } catch (error) {
    console.error('重试磁力下载任务失败:', error);
    return apiResponse.error(res, '重试磁力下载任务失败', 500, error);
  }
};