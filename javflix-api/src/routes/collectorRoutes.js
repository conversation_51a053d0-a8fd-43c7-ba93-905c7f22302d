const express = require('express');
const router = express.Router();
const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 外网API配置
const EXTERNAL_API_BASE = 'http://*************:8081';
const COLLECTOR_PATH = path.join(__dirname, '../../../data-collector');

/**
 * 检查外网API状态
 */
router.get('/external-status', async (req, res) => {
  try {
    const response = await axios.get(`${EXTERNAL_API_BASE}/health`, {
      timeout: 10000
    });
    
    res.json({
      success: true,
      data: {
        status: 'healthy',
        response: response.data,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    res.json({
      success: false,
      data: {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * 检查采集服务状态
 */
router.get('/status', async (req, res) => {
  try {
    // 检查采集服务进程
    const { exec } = require('child_process');
    
    exec('pgrep -f "data-collector"', (error, stdout, stderr) => {
      const isRunning = !error && stdout.trim().length > 0;
      
      res.json({
        success: true,
        data: {
          status: isRunning ? 'running' : 'stopped',
          pid: isRunning ? stdout.trim().split('\n')[0] : null,
          lastCollection: null, // TODO: 从日志或数据库获取
          timestamp: new Date().toISOString()
        }
      });
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 立即执行数据采集
 */
router.post('/collect-now', async (req, res) => {
  try {
    // 启动采集进程
    const collectorProcess = spawn('node', ['index.js', '--now'], {
      cwd: COLLECTOR_PATH,
      detached: true,
      stdio: 'pipe'
    });

    let output = '';
    let errorOutput = '';

    collectorProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    collectorProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    collectorProcess.on('close', (code) => {
      if (code === 0) {
        console.log('数据采集完成:', output);
      } else {
        console.error('数据采集失败:', errorOutput);
      }
    });

    // 不等待进程完成，立即返回
    res.json({
      success: true,
      message: '数据采集已启动',
      pid: collectorProcess.pid,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取数据统计
 */
router.get('/stats', async (req, res) => {
  try {
    // 获取总影片数
    const totalMovies = await prisma.movies.count();
    
    // 获取今日采集数量
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayCollected = await prisma.movies.count({
      where: {
        created_at: {
          gte: today
        }
      }
    });

    // 获取本周采集数量
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const weekCollected = await prisma.movies.count({
      where: {
        created_at: {
          gte: weekStart
        }
      }
    });

    // 获取本月采集数量
    const monthStart = new Date();
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);
    const monthCollected = await prisma.movies.count({
      where: {
        created_at: {
          gte: monthStart
        }
      }
    });

    res.json({
      success: true,
      data: {
        totalMovies,
        todayCollected,
        weekCollected,
        monthCollected,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取采集日志
 */
router.get('/logs', async (req, res) => {
  try {
    const logFile = path.join(COLLECTOR_PATH, 'logs/collector.log');
    
    try {
      const logContent = await fs.readFile(logFile, 'utf8');
      const lines = logContent.split('\n').filter(line => line.trim());
      
      // 解析日志行
      const logs = lines.slice(-100).map(line => {
        try {
          const logData = JSON.parse(line);
          return {
            timestamp: logData.timestamp,
            level: logData.level,
            message: logData.message
          };
        } catch {
          // 如果不是JSON格式，按普通文本处理
          const parts = line.split(' ');
          return {
            timestamp: parts[0] + ' ' + parts[1],
            level: 'info',
            message: line
          };
        }
      });

      res.json({
        success: true,
        data: logs.reverse() // 最新的在前面
      });

    } catch (fileError) {
      // 日志文件不存在
      res.json({
        success: true,
        data: []
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 清空日志
 */
router.delete('/logs', async (req, res) => {
  try {
    const logFile = path.join(COLLECTOR_PATH, 'logs/collector.log');
    const errorLogFile = path.join(COLLECTOR_PATH, 'logs/collector-error.log');
    
    await fs.writeFile(logFile, '');
    await fs.writeFile(errorLogFile, '');
    
    res.json({
      success: true,
      message: '日志已清空'
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 下载日志文件
 */
router.get('/logs/download', async (req, res) => {
  try {
    const logFile = path.join(COLLECTOR_PATH, 'logs/collector.log');
    
    res.download(logFile, 'collector.log', (err) => {
      if (err) {
        res.status(404).json({
          success: false,
          error: '日志文件不存在'
        });
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 获取采集配置
 */
router.get('/config', async (req, res) => {
  try {
    const configFile = path.join(COLLECTOR_PATH, '.env');
    
    try {
      const configContent = await fs.readFile(configFile, 'utf8');
      const config = {};
      
      configContent.split('\n').forEach(line => {
        if (line.includes('=') && !line.startsWith('#')) {
          const [key, value] = line.split('=');
          config[key.trim()] = value.trim().replace(/"/g, '');
        }
      });

      res.json({
        success: true,
        data: {
          batchSize: parseInt(config.BATCH_SIZE) || 50,
          interval: config.COLLECTION_INTERVAL || '0 */1 * * *',
          autoPublish: config.AUTO_PUBLISH === 'true',
          skipDuplicates: config.SKIP_DUPLICATES === 'true'
        }
      });

    } catch (fileError) {
      // 配置文件不存在，返回默认配置
      res.json({
        success: true,
        data: {
          batchSize: 50,
          interval: '0 */1 * * *',
          autoPublish: true,
          skipDuplicates: true
        }
      });
    }

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 保存采集配置
 */
router.post('/config', async (req, res) => {
  try {
    const { batchSize, interval, autoPublish, skipDuplicates } = req.body;
    const configFile = path.join(COLLECTOR_PATH, '.env');
    
    // 读取现有配置
    let configContent = '';
    try {
      configContent = await fs.readFile(configFile, 'utf8');
    } catch {
      // 文件不存在，使用默认模板
      configContent = `# JAVFLIX 数据采集服务配置
EXTERNAL_API_BASE=http://*************:8081
DATABASE_URL="postgresql://postgres:password@localhost:5432/javflix"
`;
    }

    // 更新配置值
    const updateConfig = (content, key, value) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      const newLine = `${key}=${value}`;
      
      if (regex.test(content)) {
        return content.replace(regex, newLine);
      } else {
        return content + '\n' + newLine;
      }
    };

    configContent = updateConfig(configContent, 'BATCH_SIZE', batchSize);
    configContent = updateConfig(configContent, 'COLLECTION_INTERVAL', `"${interval}"`);
    configContent = updateConfig(configContent, 'AUTO_PUBLISH', autoPublish);
    configContent = updateConfig(configContent, 'SKIP_DUPLICATES', skipDuplicates);

    await fs.writeFile(configFile, configContent);

    res.json({
      success: true,
      message: '配置保存成功'
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * 测试外网API连接
 */
router.get('/test-api/:endpoint', async (req, res) => {
  try {
    const { endpoint } = req.params;
    const validEndpoints = [
      'movies/latest',
      'movies/popular', 
      'genres',
      'actors/popular',
      'stats'
    ];

    if (!validEndpoints.includes(endpoint)) {
      return res.status(400).json({
        success: false,
        error: '无效的API端点'
      });
    }

    const response = await axios.get(`${EXTERNAL_API_BASE}/api/v1/jav/${endpoint}`, {
      timeout: 15000,
      params: { limit: 5 } // 只获取少量数据用于测试
    });

    res.json({
      success: true,
      data: {
        endpoint,
        status: response.status,
        dataCount: Array.isArray(response.data.data) ? response.data.data.length : 1,
        sample: response.data.data ? (Array.isArray(response.data.data) ? response.data.data[0] : response.data.data) : null
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      details: error.response ? error.response.data : null
    });
  }
});

module.exports = router;
