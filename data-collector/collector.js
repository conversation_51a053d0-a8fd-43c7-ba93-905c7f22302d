#!/usr/bin/env node

// 加载环境变量
require('dotenv').config();

const winston = require('winston');
const DataCollectionService = require('./src/services/DataCollectionService');

// 配置winston日志
winston.configure({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    // 文件输出
    new winston.transports.File({
      filename: process.env.LOG_FILE || 'logs/collector.log',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5
    }),
    // 错误日志
    new winston.transports.File({
      filename: process.env.ERROR_LOG_FILE || 'logs/collector-error.log',
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 3
    })
  ]
});

/**
 * JAVFLIX.TV 数据采集器主程序
 */
class DataCollector {
  constructor() {
    this.collectionService = new DataCollectionService();
    this.isShuttingDown = false;
    
    // 绑定信号处理
    this.setupSignalHandlers();
  }
  
  /**
   * 启动数据采集器
   */
  async start() {
    try {
      winston.info('🚀 JAVFLIX.TV 数据采集器启动中...');
      winston.info('📋 配置信息:', {
        nodeEnv: process.env.NODE_ENV || 'development',
        logLevel: process.env.LOG_LEVEL || 'info',
        apiUrl: process.env.EXTERNAL_API_URL || 'http://188.68.60.179:8081',
        dbUrl: process.env.DATABASE_URL ? '已配置' : '使用默认配置'
      });
      
      // 初始化服务
      await this.collectionService.initialize();
      
      // 解析命令行参数
      const args = process.argv.slice(2);
      const command = args[0];
      
      switch (command) {
        case 'start':
          await this.startDaemon();
          break;
        case 'collect':
          await this.runOnceCollection(args[1]);
          break;
        case 'status':
          await this.showStatus();
          break;
        case 'reset':
          await this.resetSyncStatus(args[1]);
          break;
        case 'logs':
          await this.showLogs(args[1], parseInt(args[2]) || 20);
          break;
        default:
          this.showHelp();
          process.exit(0);
      }
      
    } catch (error) {
      winston.error('❌ 数据采集器启动失败:', error);
      process.exit(1);
    }
  }
  
  /**
   * 启动守护进程模式
   */
  async startDaemon() {
    winston.info('🔄 启动守护进程模式...');
    
    // 启动定时采集
    this.collectionService.startScheduledCollection();
    
    // 显示状态
    const status = await this.collectionService.getCollectionStatus();
    winston.info('📊 当前状态:', {
      scheduled: status.isScheduled,
      interval: status.collectionInterval,
      apiService: status.apiServiceType,
      entities: status.syncStatistics.map(s => `${s.entity_type}(${s.status})`)
    });
    
    winston.info('✅ 守护进程已启动，按 Ctrl+C 停止');
    
    // 保持进程运行
    await this.keepAlive();
  }
  
  /**
   * 执行一次性采集
   */
  async runOnceCollection(type = 'incremental') {
    winston.info(`🔧 执行一次性${type === 'full' ? '全量' : '增量'}采集...`);
    
    try {
      const result = await this.collectionService.triggerCollection(type);
      
      winston.info('✅ 采集完成:', {
        success: result.success,
        totalTime: result.totalTime + 'ms',
        summary: result.summary || {}
      });
      
      process.exit(result.success ? 0 : 1);
      
    } catch (error) {
      winston.error('❌ 采集失败:', error);
      process.exit(1);
    }
  }
  
  /**
   * 显示状态信息
   */
  async showStatus() {
    winston.info('📊 获取状态信息...');
    
    try {
      const status = await this.collectionService.getCollectionStatus();
      
      console.log('\n=== JAVFLIX.TV 数据采集器状态 ===');
      console.log(`运行状态: ${status.isRunning ? '🟢 运行中' : '⚪ 空闲'}`);
      console.log(`定时任务: ${status.isScheduled ? '🟢 已启用' : '🔴 已禁用'}`);
      console.log(`采集间隔: ${status.collectionInterval}`);
      console.log(`API服务: ${status.apiServiceType}`);
      
      console.log('\n=== 同步统计 ===');
      status.syncStatistics.forEach(stat => {
        console.log(`${stat.entity_type}:`);
        console.log(`  状态: ${stat.status}`);
        console.log(`  上次同步: ${stat.last_sync_time || '从未同步'}`);
        console.log(`  总同步: ${stat.total_synced}`);
        console.log(`  错误数: ${stat.errors_count}`);
        console.log(`  成功率: ${stat.success_rate}%`);
        console.log(`  24h成功: ${stat.recent_success_count}`);
        console.log(`  24h错误: ${stat.recent_error_count}`);
        console.log('');
      });
      
      process.exit(0);
      
    } catch (error) {
      winston.error('❌ 获取状态失败:', error);
      process.exit(1);
    }
  }
  
  /**
   * 重置同步状态
   */
  async resetSyncStatus(entityType) {
    if (!entityType) {
      winston.error('❌ 请指定要重置的实体类型: movies, actors, genres');
      process.exit(1);
    }
    
    winston.info(`🔄 重置同步状态: ${entityType}`);
    
    try {
      await this.collectionService.syncEngine.resetSyncStatus(entityType);
      winston.info(`✅ ${entityType} 同步状态已重置`);
      process.exit(0);
      
    } catch (error) {
      winston.error('❌ 重置同步状态失败:', error);
      process.exit(1);
    }
  }
  
  /**
   * 显示同步日志
   */
  async showLogs(entityType, limit = 20) {
    winston.info(`📋 获取同步日志: ${entityType || '全部'}, 限制: ${limit}`);
    
    try {
      const logs = await this.collectionService.syncEngine.getSyncLogs(entityType, limit);
      
      console.log(`\n=== 同步日志 (最近${logs.length}条) ===`);
      logs.forEach((log, index) => {
        const time = new Date(log.created_at).toLocaleString('zh-CN');
        const status = log.action === 'error' ? '❌' : 
                      log.action === 'create' ? '🆕' : 
                      log.action === 'update' ? '🔄' : '✅';
        
        console.log(`${index + 1}. ${status} [${log.entity_type}] ${log.action} - ${log.entity_id || 'N/A'}`);
        console.log(`   时间: ${time}`);
        
        if (log.error_message) {
          console.log(`   错误: ${log.error_message}`);
        }
        
        if (log.processing_time) {
          console.log(`   耗时: ${log.processing_time}ms`);
        }
        
        console.log('');
      });
      
      process.exit(0);
      
    } catch (error) {
      winston.error('❌ 获取日志失败:', error);
      process.exit(1);
    }
  }
  
  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(`
JAVFLIX.TV 数据采集器

用法:
  node collector.js <command> [options]

命令:
  start                    启动守护进程模式，定时执行增量采集
  collect [type]           执行一次性采集
                          type: incremental (默认) | full
  status                   显示当前状态和统计信息
  reset <entity>           重置指定实体的同步状态
                          entity: movies | actors | genres
  logs [entity] [limit]    显示同步日志
                          entity: movies | actors | genres (可选)
                          limit: 显示条数 (默认20)

示例:
  node collector.js start                    # 启动守护进程
  node collector.js collect                  # 执行增量采集
  node collector.js collect full             # 执行全量采集
  node collector.js status                   # 查看状态
  node collector.js reset movies             # 重置影片同步状态
  node collector.js logs movies 50           # 查看影片同步日志

环境变量:
  DATABASE_URL             PostgreSQL数据库连接字符串
  EXTERNAL_API_URL         外网API地址
  LOG_LEVEL               日志级别 (debug|info|warn|error)
  COLLECTION_INTERVAL     采集间隔 (cron格式)
  BATCH_SIZE_MOVIES       影片批次大小
  BATCH_SIZE_ACTORS       演员批次大小
  BATCH_SIZE_GENRES       分类批次大小
`);
  }
  
  /**
   * 保持进程运行
   */
  async keepAlive() {
    return new Promise((resolve) => {
      // 进程会一直运行直到收到退出信号
      process.on('SIGINT', resolve);
      process.on('SIGTERM', resolve);
    });
  }
  
  /**
   * 设置信号处理器
   */
  setupSignalHandlers() {
    const gracefulShutdown = async (signal) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;
      
      winston.info(`📡 收到 ${signal} 信号，开始优雅关闭...`);
      
      try {
        await this.collectionService.shutdown();
        winston.info('✅ 数据采集器已安全关闭');
        process.exit(0);
      } catch (error) {
        winston.error('❌ 关闭过程中发生错误:', error);
        process.exit(1);
      }
    };
    
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    
    process.on('uncaughtException', (error) => {
      winston.error('💥 未捕获的异常:', error);
      process.exit(1);
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      winston.error('💥 未处理的Promise拒绝:', { reason, promise });
      process.exit(1);
    });
  }
}

// 启动数据采集器
if (require.main === module) {
  const collector = new DataCollector();
  collector.start();
}

module.exports = DataCollector;
