# 外网API配置
EXTERNAL_API_URL=http://*************:8081
API_TIMEOUT=30000
API_MAX_RETRIES=3
API_RETRY_DELAY=1000

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/javflix

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/collector.log
ERROR_LOG_FILE=logs/collector-error.log

# 同步配置
SYNC_INTERVAL=3600000
BATCH_SIZE_MOVIES=100
BATCH_SIZE_ACTORS=50
BATCH_SIZE_GENRES=20

# 监控配置
HEALTH_CHECK_INTERVAL=300000
PERFORMANCE_METRICS_ENABLED=true
