#!/usr/bin/env node

/**
 * JAVFLIX.TV 外网数据采集服务
 * 从 http://188.68.60.179:8081 采集数据到本地数据库
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');
const cron = require('node-cron');
const winston = require('winston');

// 配置
const CONFIG = {
  EXTERNAL_API_BASE: 'http://188.68.60.179:8081',
  BATCH_SIZE: 50,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 5000,
  COLLECTION_INTERVAL: '0 */1 * * *', // 每小时执行一次
};

// 日志配置
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/collector-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/collector.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// 数据库连接
const prisma = new PrismaClient();

class DataCollector {
  constructor() {
    this.apiClient = axios.create({
      baseURL: CONFIG.EXTERNAL_API_BASE,
      timeout: 30000,
      headers: {
        'User-Agent': 'JAVFLIX-Collector/1.0',
        'Accept': 'application/json'
      }
    });
  }

  /**
   * 主要采集方法
   */
  async collectAll() {
    logger.info('🚀 开始数据采集...');
    
    try {
      // 1. 采集最新影片
      await this.collectLatestMovies();
      
      // 2. 采集热门影片
      await this.collectPopularMovies();
      
      // 3. 采集分类信息
      await this.collectGenres();
      
      // 4. 采集演员信息
      await this.collectActors();
      
      // 5. 更新统计信息
      await this.updateStats();
      
      logger.info('✅ 数据采集完成');
      
    } catch (error) {
      logger.error('❌ 数据采集失败:', error);
      throw error;
    }
  }

  /**
   * 采集最新影片
   */
  async collectLatestMovies() {
    logger.info('📥 采集最新影片...');
    
    try {
      const response = await this.apiClient.get('/api/v1/jav/movies/latest', {
        params: { limit: CONFIG.BATCH_SIZE }
      });
      
      const movies = response.data.data || response.data;
      logger.info(`获取到 ${movies.length} 部最新影片`);
      
      for (const movieData of movies) {
        await this.saveMovie(movieData);
      }
      
    } catch (error) {
      logger.error('采集最新影片失败:', error.message);
      throw error;
    }
  }

  /**
   * 采集热门影片
   */
  async collectPopularMovies() {
    logger.info('🔥 采集热门影片...');
    
    try {
      const response = await this.apiClient.get('/api/v1/jav/movies/popular', {
        params: { limit: CONFIG.BATCH_SIZE }
      });
      
      const movies = response.data.data || response.data;
      logger.info(`获取到 ${movies.length} 部热门影片`);
      
      for (const movieData of movies) {
        await this.saveMovie(movieData);
      }
      
    } catch (error) {
      logger.error('采集热门影片失败:', error.message);
      throw error;
    }
  }

  /**
   * 保存影片到数据库
   */
  async saveMovie(movieData) {
    try {
      // 检查是否已存在
      const existing = await prisma.movies.findFirst({
        where: {
          OR: [
            { title: movieData.title },
            { code: movieData.code }
          ]
        }
      });

      if (existing) {
        logger.debug(`影片已存在: ${movieData.title}`);
        return existing;
      }

      // 创建新影片记录
      const movie = await prisma.movies.create({
        data: {
          title: movieData.title,
          code: movieData.code,
          description: movieData.description || '',
          cover_url: movieData.cover_url || movieData.coverUrl,
          release_date: movieData.release_date ? new Date(movieData.release_date) : null,
          duration: movieData.duration || 0,
          rating: movieData.rating || 0,
          view_count: movieData.view_count || 0,
          status: 'published', // 直接发布，不需要处理
          created_at: new Date(),
          updated_at: new Date()
        }
      });

      // 保存磁力链接
      if (movieData.magnets && movieData.magnets.length > 0) {
        for (const magnetData of movieData.magnets) {
          await this.saveMagnet(movie.id, magnetData);
        }
      }

      // 保存演员关联
      if (movieData.stars && movieData.stars.length > 0) {
        for (const starData of movieData.stars) {
          await this.saveMovieStar(movie.id, starData);
        }
      }

      // 保存分类关联
      if (movieData.genres && movieData.genres.length > 0) {
        for (const genreData of movieData.genres) {
          await this.saveMovieGenre(movie.id, genreData);
        }
      }

      logger.info(`✅ 保存影片: ${movie.title}`);
      return movie;

    } catch (error) {
      logger.error(`保存影片失败 ${movieData.title}:`, error.message);
      throw error;
    }
  }

  /**
   * 保存磁力链接
   */
  async saveMagnet(movieId, magnetData) {
    try {
      const existing = await prisma.magnets.findFirst({
        where: {
          movie_id: movieId,
          magnet_link: magnetData.magnet_link || magnetData.link
        }
      });

      if (!existing) {
        await prisma.magnets.create({
          data: {
            movie_id: movieId,
            magnet_link: magnetData.magnet_link || magnetData.link,
            file_size: magnetData.file_size || magnetData.size || '',
            quality: magnetData.quality || 'HD',
            created_at: new Date()
          }
        });
      }
    } catch (error) {
      logger.error('保存磁力链接失败:', error.message);
    }
  }

  /**
   * 采集分类信息
   */
  async collectGenres() {
    logger.info('📂 采集分类信息...');
    
    try {
      const response = await this.apiClient.get('/api/v1/jav/genres');
      const genres = response.data.data || response.data;
      
      for (const genreData of genres) {
        await this.saveGenre(genreData);
      }
      
      logger.info(`✅ 采集了 ${genres.length} 个分类`);
      
    } catch (error) {
      logger.error('采集分类失败:', error.message);
    }
  }

  /**
   * 保存分类
   */
  async saveGenre(genreData) {
    try {
      const existing = await prisma.genres.findFirst({
        where: { name: genreData.name }
      });

      if (!existing) {
        await prisma.genres.create({
          data: {
            name: genreData.name,
            description: genreData.description || '',
            created_at: new Date()
          }
        });
      }
    } catch (error) {
      logger.error('保存分类失败:', error.message);
    }
  }

  /**
   * 采集演员信息
   */
  async collectActors() {
    logger.info('👥 采集演员信息...');
    
    try {
      const response = await this.apiClient.get('/api/v1/jav/actors/popular');
      const actors = response.data.data || response.data;
      
      for (const actorData of actors) {
        await this.saveActor(actorData);
      }
      
      logger.info(`✅ 采集了 ${actors.length} 个演员`);
      
    } catch (error) {
      logger.error('采集演员失败:', error.message);
    }
  }

  /**
   * 保存演员
   */
  async saveActor(actorData) {
    try {
      const existing = await prisma.stars.findFirst({
        where: { name: actorData.name }
      });

      if (!existing) {
        await prisma.stars.create({
          data: {
            name: actorData.name,
            avatar_url: actorData.avatar_url || actorData.avatarUrl,
            description: actorData.description || '',
            created_at: new Date()
          }
        });
      }
    } catch (error) {
      logger.error('保存演员失败:', error.message);
    }
  }

  /**
   * 更新统计信息
   */
  async updateStats() {
    logger.info('📊 更新统计信息...');
    
    try {
      const movieCount = await prisma.movies.count();
      const starCount = await prisma.stars.count();
      const genreCount = await prisma.genres.count();
      
      logger.info(`📈 当前统计: 影片 ${movieCount} 部, 演员 ${starCount} 人, 分类 ${genreCount} 个`);
      
    } catch (error) {
      logger.error('更新统计失败:', error.message);
    }
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const response = await this.apiClient.get('/health');
      logger.info('✅ 外网API服务正常:', response.data);
      return true;
    } catch (error) {
      logger.error('❌ 外网API服务异常:', error.message);
      return false;
    }
  }
}

// 主程序
async function main() {
  const collector = new DataCollector();
  
  // 健康检查
  const isHealthy = await collector.healthCheck();
  if (!isHealthy) {
    logger.error('外网API不可用，退出程序');
    process.exit(1);
  }
  
  // 立即执行一次采集
  if (process.argv.includes('--now')) {
    await collector.collectAll();
    process.exit(0);
  }
  
  // 定时任务
  cron.schedule(CONFIG.COLLECTION_INTERVAL, async () => {
    try {
      await collector.collectAll();
    } catch (error) {
      logger.error('定时采集失败:', error);
    }
  });
  
  logger.info(`🕐 定时采集已启动，执行间隔: ${CONFIG.COLLECTION_INTERVAL}`);
  logger.info('💡 使用 --now 参数可立即执行一次采集');
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', async () => {
  logger.info('收到退出信号，正在关闭...');
  await prisma.$disconnect();
  process.exit(0);
});

// 启动程序
if (require.main === module) {
  main().catch(error => {
    logger.error('程序启动失败:', error);
    process.exit(1);
  });
}

module.exports = DataCollector;
