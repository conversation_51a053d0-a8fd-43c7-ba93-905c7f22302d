const winston = require('winston');
const ApiService = require('./src/services/ApiService');

// 配置winston日志
winston.configure({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

/**
 * 测试ApiService的各项功能
 */
async function testApiService() {
  console.log('🚀 开始测试外网API服务...\n');
  
  const apiService = new ApiService();
  
  // 显示配置信息
  console.log('📋 API配置信息:');
  console.log(JSON.stringify(apiService.getConfig(), null, 2));
  console.log('');
  
  // 测试1: 健康检查
  console.log('🔍 测试1: 健康检查');
  try {
    const healthStatus = await apiService.healthCheck();
    console.log('✅ 健康检查结果:', JSON.stringify(healthStatus, null, 2));
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message);
  }
  console.log('');
  
  // 测试2: 获取最新影片
  console.log('🔍 测试2: 获取最新影片');
  try {
    const movies = await apiService.getLatestMovies({ page: 1, limit: 5 });
    console.log('✅ 最新影片获取成功，数量:', movies?.data?.length || 0);
    if (movies?.data?.length > 0) {
      console.log('首个影片示例:', JSON.stringify(movies.data[0], null, 2));
    }
  } catch (error) {
    const errorInfo = apiService.handleError(error);
    console.log('❌ 获取最新影片失败:', errorInfo.description);
    console.log('错误详情:', JSON.stringify(errorInfo, null, 2));
  }
  console.log('');
  
  // 测试3: 获取热门影片
  console.log('🔍 测试3: 获取热门影片');
  try {
    const movies = await apiService.getPopularMovies({ page: 1, limit: 5 });
    console.log('✅ 热门影片获取成功，数量:', movies?.data?.length || 0);
  } catch (error) {
    const errorInfo = apiService.handleError(error);
    console.log('❌ 获取热门影片失败:', errorInfo.description);
  }
  console.log('');
  
  // 测试4: 获取演员列表
  console.log('🔍 测试4: 获取演员列表');
  try {
    const actors = await apiService.getActors({ page: 1, limit: 5 });
    console.log('✅ 演员列表获取成功，数量:', actors?.data?.length || 0);
  } catch (error) {
    const errorInfo = apiService.handleError(error);
    console.log('❌ 获取演员列表失败:', errorInfo.description);
  }
  console.log('');
  
  // 测试5: 获取分类列表
  console.log('🔍 测试5: 获取分类列表');
  try {
    const genres = await apiService.getGenres({ page: 1, limit: 10 });
    console.log('✅ 分类列表获取成功，数量:', genres?.data?.length || 0);
  } catch (error) {
    const errorInfo = apiService.handleError(error);
    console.log('❌ 获取分类列表失败:', errorInfo.description);
  }
  console.log('');
  
  // 测试6: 获取服务器统计
  console.log('🔍 测试6: 获取服务器统计');
  try {
    const stats = await apiService.getServerStats();
    console.log('✅ 服务器统计获取成功:', JSON.stringify(stats, null, 2));
  } catch (error) {
    const errorInfo = apiService.handleError(error);
    console.log('❌ 获取服务器统计失败:', errorInfo.description);
  }
  console.log('');
  
  // 测试7: 错误处理机制
  console.log('🔍 测试7: 错误处理机制（测试不存在的端点）');
  try {
    await apiService.client.get('/api/v1/nonexistent');
  } catch (error) {
    const errorInfo = apiService.handleError(error);
    console.log('✅ 错误处理机制正常工作');
    console.log('错误类型:', errorInfo.type);
    console.log('错误描述:', errorInfo.description);
  }
  console.log('');
  
  console.log('🎉 API服务测试完成！');
}

// 运行测试
if (require.main === module) {
  testApiService().catch(error => {
    console.error('测试过程中发生未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = { testApiService };
