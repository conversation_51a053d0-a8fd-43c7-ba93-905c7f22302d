# JAVFLIX.TV 数据采集器

## 概述

JAVFLIX.TV 数据采集器是一个完整的数据同步系统，负责从外网API采集影片、演员和分类数据，并同步到本地PostgreSQL数据库。

## 功能特性

### 🚀 核心功能
- **增量同步**：基于时间戳的智能增量同步，只同步新增和更新的数据
- **全量同步**：初始化或重置时的完整数据同步
- **自适应API**：智能检测外网API可用性，自动切换到模拟服务
- **数据验证**：完善的数据质量验证和清洗机制
- **错误处理**：完整的错误处理和重试机制

### 📊 监控和管理
- **实时状态**：查看同步状态、成功率、错误统计
- **性能监控**：API响应时间、处理速度、批次大小优化
- **日志管理**：详细的操作日志，支持查询和自动清理
- **定时任务**：支持cron表达式的定时采集

### 🔧 技术架构
- **模块化设计**：AdaptiveApiService、DataMapper、ValidationService、SyncEngine
- **事务安全**：完整的数据库事务管理，确保数据一致性
- **批量处理**：可配置的批次大小，优化性能和内存使用
- **优雅关闭**：支持信号处理和优雅关闭

## 快速开始

### 1. 环境要求
- Node.js 16+
- PostgreSQL 12+
- 网络连接到外网API服务器

### 2. 安装依赖
```bash
cd data-collector
npm install
```

### 3. 配置环境
复制并编辑环境配置文件：
```bash
cp .env.example .env
```

主要配置项：
```bash
# 数据库连接
DATABASE_URL=postgresql://postgres:password@localhost:5432/javflix

# 外网API
EXTERNAL_API_URL=http://*************:8081

# 采集配置
COLLECTION_INTERVAL="0 */1 * * *"  # 每小时执行一次
BATCH_SIZE_MOVIES=50
BATCH_SIZE_ACTORS=30
BATCH_SIZE_GENRES=20
```

### 4. 运行采集器

#### 查看帮助
```bash
node collector.js
```

#### 查看当前状态
```bash
node collector.js status
```

#### 执行一次性采集
```bash
# 增量采集
node collector.js collect

# 全量采集（初始化）
node collector.js collect full
```

#### 启动守护进程
```bash
node collector.js start
```

## 使用指南

### 命令行接口

#### 状态查看
```bash
node collector.js status
```
显示：
- 运行状态和定时任务状态
- 各实体类型的同步统计
- API服务类型和配置信息

#### 日志查看
```bash
# 查看所有日志（最近20条）
node collector.js logs

# 查看特定实体的日志
node collector.js logs movies 10
node collector.js logs actors 20
node collector.js logs genres 5
```

#### 同步状态管理
```bash
# 重置同步状态
node collector.js reset movies
node collector.js reset actors
node collector.js reset genres
```

### 采集结果

#### 最近一次采集统计
- **影片数据**：处理1000部，成功1000部，成功率100%
- **演员数据**：处理510位，成功425位，成功率83.33%
- **分类数据**：处理10个，成功10个，成功率100%
- **总体成功率**：94.41%
- **总耗时**：44.3秒

#### 数据库统计
- **影片总数**：1,050部
- **演员总数**：425位
- **分类总数**：10个

### 定时采集

#### 启动定时任务
```bash
node collector.js start
```

#### 默认采集间隔
- **频率**：每小时执行一次增量采集
- **时间**：每小时的0分钟（如：01:00, 02:00, 03:00...）
- **类型**：增量同步（只同步新增和更新的数据）

#### 自定义采集间隔
编辑 `.env` 文件中的 `COLLECTION_INTERVAL`：
```bash
# 每30分钟执行一次
COLLECTION_INTERVAL="*/30 * * * *"

# 每天凌晨2点执行
COLLECTION_INTERVAL="0 2 * * *"

# 每周一凌晨3点执行
COLLECTION_INTERVAL="0 3 * * 1"
```

## 监控和维护

### 性能监控
- **API响应时间**：平均100-200ms
- **数据处理速度**：约30-50条/秒
- **内存使用**：批量处理优化，避免内存溢出
- **错误率**：正常情况下<5%

### 日志管理
- **操作日志**：记录每次同步操作的详细信息
- **错误日志**：记录失败原因和错误堆栈
- **性能日志**：记录处理时间和性能指标
- **自动清理**：默认保留30天的日志记录

### 故障排除

#### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库连接
   psql -h localhost -U postgres -d javflix -c "SELECT 1"
   ```

2. **外网API不可用**
   - 系统会自动切换到模拟API服务
   - 查看日志确认切换状态
   - 检查网络连接和API服务器状态

3. **同步失败**
   ```bash
   # 查看错误日志
   node collector.js logs movies 20
   
   # 重置同步状态
   node collector.js reset movies
   ```

4. **性能问题**
   - 调整批次大小：减小 `BATCH_SIZE_*` 配置
   - 增加采集间隔：修改 `COLLECTION_INTERVAL`
   - 检查数据库性能和索引

### 数据质量

#### 验证规则
- **必填字段**：movie_id、title、star_id、name等
- **数据格式**：日期格式、URL格式、数值范围
- **业务逻辑**：年龄一致性、出道日期合理性
- **重复检查**：基于唯一标识符去重

#### 质量报告
每次采集后会生成详细的质量报告：
- 总处理数量和成功率
- 错误类型分布和统计
- 数据质量建议和改进方向

## 开发和扩展

### 项目结构
```
data-collector/
├── src/
│   ├── services/          # 核心服务
│   │   ├── AdaptiveApiService.js    # 自适应API服务
│   │   ├── DataCollectionService.js # 数据采集服务
│   │   ├── SyncEngine.js           # 同步引擎
│   │   └── ValidationService.js    # 数据验证服务
│   └── mappers/           # 数据映射
│       └── DataMapper.js           # 数据映射器
├── logs/                  # 日志文件
├── collector.js          # 主程序入口
├── package.json          # 依赖配置
└── .env                  # 环境配置
```

### 扩展新的数据类型
1. 在 `DataMapper.js` 中添加映射方法
2. 在 `ValidationService.js` 中添加验证规则
3. 在 `SyncEngine.js` 中添加保存方法
4. 更新数据库表结构

### 自定义API适配器
1. 继承 `AdaptiveApiService` 类
2. 实现特定的API端点方法
3. 添加错误处理和重试逻辑
4. 配置API认证和限流

## 许可证

本项目为 JAVFLIX.TV 内部使用，版权所有。
