generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model categories {
  id          Int       @id @default(autoincrement())
  name        String
  slug        String    @unique
  image_url   String?
  count       Int?      @default(0)
  description String?
  color       String?
  icon        String?
  is_featured Boolean?  @default(false)
  created_at  DateTime? @default(now()) @db.Timestamp(6)
  updated_at  DateTime? @default(now()) @db.Timestamp(6)
}

model directors {
  id              Int               @id @default(autoincrement())
  director_id     String            @unique @db.VarChar(50)
  name            String            @db.VarChar(100)
  created_at      DateTime          @db.Timestamp(6)
  updated_at      DateTime          @db.Timestamp(6)
  movie_directors movie_directors[]
  movies          movies[]
}

model genres {
  id           Int            @id @default(autoincrement())
  name         String         @unique @db.VarChar(100)
  created_at   DateTime       @db.Timestamp(6)
  updated_at   DateTime       @db.Timestamp(6)
  movie_genres movie_genres[]
}

model magnets {
  id           Int      @id @default(autoincrement())
  movie_id     Int?
  magnet_id    String?  @db.VarChar(100)
  link         String
  title        String?
  size         String?  @db.VarChar(50)
  is_hd        Boolean? @default(false)
  has_subtitle Boolean? @default(false)
  share_date   String?  @db.VarChar(50)
  created_at   DateTime @db.Timestamp(6)
  updated_at   DateTime @db.Timestamp(6)
  movies       movies?  @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model movie_directors {
  movie_id    Int
  director_id Int
  directors   directors @relation(fields: [director_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  movies      movies    @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([movie_id, director_id])
}

model movie_genres {
  movie_id Int
  genre_id Int
  genres   genres @relation(fields: [genre_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  movies   movies @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([movie_id, genre_id])
}

model movie_stars {
  movie_id Int
  star_id  Int
  movies   movies @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  stars    stars  @relation(fields: [star_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([movie_id, star_id])
}

model movies {
  id                      Int               @id @default(autoincrement())
  movie_id                String            @unique @db.VarChar(50)
  title                   String
  image_url               String?
  cover_image             String?
  release_date            String?           @db.VarChar(50)
  duration                String?           @db.VarChar(50)
  description             String?
  director_id             Int?
  producer_id             Int?
  publisher_id            Int?
  series_id               Int?
  created_at              DateTime          @db.Timestamp(6)
  updated_at              DateTime          @db.Timestamp(6)
  cached_image_url        String?
  view_count              Int?              @default(0)
  // 新增状态管理字段
  status                  String?           @default("draft") @db.VarChar(20)
  video_urls              Json?             @default("{}")
  processing_priority     Int?              @default(5)
  published_at            DateTime?         @db.Timestamp(6)
  processing_started_at   DateTime?         @db.Timestamp(6)
  processing_completed_at DateTime?         @db.Timestamp(6)
  // 关联关系
  magnets                 magnets[]
  movie_directors         movie_directors[]
  movie_genres            movie_genres[]
  movie_stars             movie_stars[]
  directors               directors?        @relation(fields: [director_id], references: [id], onUpdate: NoAction)
  producers               producers?        @relation(fields: [producer_id], references: [id], onUpdate: NoAction)
  publishers              publishers?       @relation(fields: [publisher_id], references: [id], onUpdate: NoAction)
  series                  series?           @relation(fields: [series_id], references: [id], onUpdate: NoAction)
  samples                 samples[]
  similar_movies          similar_movies[]
  user_likes              user_likes[]
  video_processing_tasks  video_processing_tasks[]

  @@index([cached_image_url], map: "idx_movies_cached_image_url")
  @@index([status], map: "idx_movies_status")
  @@index([published_at], map: "idx_movies_published_at")
  @@index([processing_priority], map: "idx_movies_processing_priority")
}

model producers {
  id          Int      @id @default(autoincrement())
  producer_id String   @unique @db.VarChar(50)
  name        String   @db.VarChar(100)
  created_at  DateTime @db.Timestamp(6)
  updated_at  DateTime @db.Timestamp(6)
  movies      movies[]
}

model publishers {
  id           Int      @id @default(autoincrement())
  publisher_id String   @unique @db.VarChar(50)
  name         String   @db.VarChar(100)
  created_at   DateTime @db.Timestamp(6)
  updated_at   DateTime @db.Timestamp(6)
  movies       movies[]
}

model samples {
  id         Int      @id @default(autoincrement())
  movie_id   Int?
  sample_id  String?  @db.VarChar(50)
  alt        String?
  created_at DateTime @db.Timestamp(6)
  updated_at DateTime @db.Timestamp(6)
  movies     movies?  @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model series {
  id         Int      @id @default(autoincrement())
  series_id  String   @unique @db.VarChar(50)
  name       String   @db.VarChar(200)
  created_at DateTime @db.Timestamp(6)
  updated_at DateTime @db.Timestamp(6)
  movies     movies[]
}

model similar_movies {
  id               Int      @id @default(autoincrement())
  movie_id         Int?
  similar_movie_id String   @db.VarChar(50)
  similar_title    String?
  similar_image    String?
  created_at       DateTime @db.Timestamp(6)
  updated_at       DateTime @db.Timestamp(6)
  movies           movies?  @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model stars {
  id               Int           @id @default(autoincrement())
  star_id          String        @unique @db.VarChar(50)
  name             String        @db.VarChar(100)
  image_url        String?
  created_at       DateTime      @db.Timestamp(6)
  updated_at       DateTime      @db.Timestamp(6)
  cached_image_url String?
  birthday         String?       @db.VarChar(50)
  age              Int?
  height           Int?
  bust             String?       @db.VarChar(50)
  waist            String?       @db.VarChar(50)
  hip              String?       @db.VarChar(50)
  birthplace       String?       @db.VarChar(100)
  hobby            String?
  waistline        String?       @db.VarChar(50)
  hipline          String?       @db.VarChar(50)
  cup_size         String?       @db.VarChar(10)
  movie_count      Int?
  debut_date       String?       @db.VarChar(50)
  measurements     String?       @db.VarChar(100)
  description      String?
  javbus_id        String?       @db.VarChar(50)
  bust_size        String?       @db.VarChar(50)
  waist_size       String?       @db.VarChar(50)
  hip_size         String?       @db.VarChar(50)
  movie_stars      movie_stars[]

  @@index([cached_image_url], map: "idx_stars_cached_image_url")
}

model users {
  id             Int              @id @default(autoincrement())
  username       String           @unique @db.VarChar(50)
  email          String           @unique @db.VarChar(100)
  password       String           @db.VarChar(255)
  is_admin       Boolean?         @default(false)
  created_at     DateTime?        @default(now()) @db.Timestamp(6)
  lastLoginAt    DateTime?        @db.Timestamp(6)
  updated_at     DateTime?        @db.Timestamp(6)
  updatedAt      DateTime?        @db.Timestamp(6)
  createdAt      DateTime?        @db.Timestamp(6)
  is_active      Boolean?         @default(true)
  user_favorites user_favorites[]
  user_following user_following[]
  user_likes     user_likes[]
  watch_history  watch_history[]
}

model video_categories {
  video_id    Int
  category_id Int

  @@id([video_id, category_id])
}

model user_favorites {
  id         Int       @id @default(autoincrement())
  user_id    Int?
  video_id   String    @db.VarChar(50)
  note       String?
  created_at DateTime? @default(now()) @db.Timestamp(6)
  users      users?    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model user_following {
  id         Int       @id @default(autoincrement())
  user_id    Int?
  star_id    Int
  created_at DateTime? @default(now()) @db.Timestamp(6)
  users      users?    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model user_likes {
  id         Int      @id @default(autoincrement())
  user_id    Int
  video_id   Int
  created_at DateTime @default(now()) @db.Timestamp(6)
  users      users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  movies     movies   @relation(fields: [video_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, video_id])
  @@index([created_at], map: "idx_user_likes_created_at")
  @@index([user_id], map: "idx_user_likes_user_id")
  @@index([video_id], map: "idx_user_likes_video_id")
}

model watch_history {
  id         Int       @id @default(autoincrement())
  user_id    Int?
  video_id   Int
  progress   Int?      @default(0)
  completed  Boolean?  @default(false)
  watched_at DateTime? @default(now()) @db.Timestamp(6)
  users      users?    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model video_stats {
  id         Int      @id @default(autoincrement())
  views      Int      @default(0)
  likes      Int      @default(0)
  favorites  Int      @default(0)
  created_at DateTime @default(now()) @db.Timestamp(6)
  updated_at DateTime @default(now()) @db.Timestamp(6)

  @@index([views], map: "idx_video_stats_views")
  @@index([likes], map: "idx_video_stats_likes")
  @@index([favorites], map: "idx_video_stats_favorites")
}

model video_processing_tasks {
  id            Int       @id @default(autoincrement())
  task_uuid     String    @unique @default(uuid()) @db.Uuid
  movie_id      Int
  status        String    @default("pending") @db.VarChar(20)
  priority      Int       @default(5)
  config        Json      @default("{}")
  progress      Int       @default(0)
  error_message String?
  started_at    DateTime? @db.Timestamp(6)
  completed_at  DateTime? @db.Timestamp(6)
  created_at    DateTime  @default(now()) @db.Timestamp(6)
  updated_at    DateTime  @default(now()) @db.Timestamp(6)
  movies        movies    @relation(fields: [movie_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([status], map: "idx_video_tasks_status")
  @@index([movie_id], map: "idx_video_tasks_movie_id")
  @@index([priority], map: "idx_video_tasks_priority")
  @@index([task_uuid], map: "idx_video_tasks_uuid")
}

model movie_status_logs {
  id         Int       @id @default(autoincrement())
  movie_id   Int
  old_status String?   @db.VarChar(20)
  new_status String    @db.VarChar(20)
  changed_by String?   @db.VarChar(100)
  reason     String?
  created_at DateTime  @default(now()) @db.Timestamp(6)

  @@index([movie_id], map: "idx_status_logs_movie_id")
  @@index([created_at], map: "idx_status_logs_created_at")
}
