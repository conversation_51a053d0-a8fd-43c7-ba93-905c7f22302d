{"name": "javflix-data-collector", "version": "1.0.0", "description": "JAVFLIX.TV 外网数据采集服务", "main": "index.js", "scripts": {"start": "node index.js", "collect-now": "node index.js --now", "dev": "nodemon index.js", "test": "jest", "logs": "tail -f logs/collector.log"}, "dependencies": {"axios": "^1.6.0", "@prisma/client": "^5.7.0", "node-cron": "^3.0.3", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["javflix", "data-collector", "api", "scraper"], "author": "JAVFLIX Team", "license": "MIT"}