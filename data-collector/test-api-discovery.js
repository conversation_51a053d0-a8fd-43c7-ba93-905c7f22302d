const winston = require('winston');
const ApiService = require('./src/services/ApiService');

// 配置winston日志
winston.configure({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

/**
 * 测试API端点发现功能
 */
async function testApiDiscovery() {
  console.log('🔍 开始API端点发现测试...\n');
  
  const apiService = new ApiService();
  
  // 测试1: 健康检查
  console.log('🔍 测试1: 健康检查');
  try {
    const healthStatus = await apiService.healthCheck();
    console.log('✅ 健康检查结果:', JSON.stringify(healthStatus, null, 2));
  } catch (error) {
    console.log('❌ 健康检查失败:', error.message);
  }
  console.log('');
  
  // 测试2: 端点发现
  console.log('🔍 测试2: API端点发现');
  try {
    const discovery = await apiService.discoverEndpoints();
    
    console.log('📊 端点发现结果:');
    console.log(`✅ 可用端点 (${discovery.available.length}个):`);
    discovery.available.forEach(endpoint => {
      console.log(`  - ${endpoint.endpoint} (${endpoint.status}) - ${endpoint.dataSize} bytes`);
    });
    
    console.log(`\n❌ 不可用端点 (${discovery.unavailable.length}个):`);
    discovery.unavailable.forEach(endpoint => {
      console.log(`  - ${endpoint.endpoint} (${endpoint.status}) - ${endpoint.error}`);
    });
    
    if (discovery.errors.length > 0) {
      console.log(`\n💥 错误端点 (${discovery.errors.length}个):`);
      discovery.errors.forEach(endpoint => {
        console.log(`  - ${endpoint.endpoint} - ${endpoint.error}`);
      });
    }
    
    // 如果有可用端点，尝试获取数据
    if (discovery.available.length > 0) {
      console.log('\n🔍 测试可用端点数据获取:');
      for (const endpoint of discovery.available.slice(0, 3)) { // 只测试前3个
        try {
          console.log(`\n📡 测试端点: ${endpoint.endpoint}`);
          const data = await apiService.callApi(endpoint.endpoint, { limit: 5 });
          console.log(`✅ 数据获取成功，类型: ${typeof data}, 大小: ${JSON.stringify(data).length} bytes`);
          
          // 显示数据结构
          if (typeof data === 'object' && data !== null) {
            const keys = Object.keys(data);
            console.log(`📋 数据结构: {${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}}`);
            
            // 如果是数组，显示第一个元素的结构
            if (Array.isArray(data) && data.length > 0) {
              const firstItem = data[0];
              if (typeof firstItem === 'object') {
                const itemKeys = Object.keys(firstItem);
                console.log(`📋 数组元素结构: {${itemKeys.slice(0, 5).join(', ')}${itemKeys.length > 5 ? '...' : ''}}`);
              }
            }
          }
        } catch (error) {
          console.log(`❌ 端点 ${endpoint.endpoint} 数据获取失败:`, error.message);
        }
      }
    }
    
  } catch (error) {
    console.log('❌ 端点发现失败:', error.message);
  }
  console.log('');
  
  // 测试3: 灵活端点方法
  console.log('🔍 测试3: 灵活端点方法');
  
  const testMethods = [
    { name: '最新影片', method: 'getLatestMovies' },
    { name: '热门影片', method: 'getPopularMovies' },
    { name: '演员列表', method: 'getActors' },
    { name: '分类列表', method: 'getGenres' }
  ];
  
  for (const test of testMethods) {
    try {
      console.log(`\n📡 测试${test.name}...`);
      const result = await apiService[test.method]({ limit: 3 });
      console.log(`✅ ${test.name}获取成功，数据大小: ${JSON.stringify(result).length} bytes`);
    } catch (error) {
      console.log(`❌ ${test.name}获取失败: ${error.message}`);
    }
  }
  
  console.log('\n🎉 API端点发现测试完成！');
}

// 运行测试
if (require.main === module) {
  testApiDiscovery().catch(error => {
    console.error('测试过程中发生未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = { testApiDiscovery };
