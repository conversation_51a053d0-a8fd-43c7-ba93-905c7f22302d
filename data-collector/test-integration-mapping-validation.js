const winston = require('winston');
const AdaptiveApiService = require('./src/services/AdaptiveApiService');
const DataMapper = require('./src/mappers/DataMapper');
const ValidationService = require('./src/services/ValidationService');

// 配置winston日志
winston.configure({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

/**
 * 测试API服务、数据映射和验证的集成
 */
async function testIntegrationMappingValidation() {
  console.log('🚀 开始测试API服务、数据映射和验证的集成...\n');
  
  const adaptiveApi = new AdaptiveApiService();
  const dataMapper = new DataMapper();
  const validationService = new ValidationService();
  
  // 初始化API服务
  await adaptiveApi.initialize();
  
  // 测试1: 完整的数据流程 - 影片数据
  console.log('🔍 测试1: 完整的影片数据流程（API → 映射 → 验证）');
  try {
    // 1. 从API获取数据
    console.log('📡 从API获取影片数据...');
    const apiMovies = await adaptiveApi.getLatestMovies({ limit: 5 });
    console.log(`✅ API返回 ${apiMovies.data?.length || 0} 部影片`);
    
    if (apiMovies.data && apiMovies.data.length > 0) {
      // 2. 数据映射
      console.log('🔄 进行数据映射...');
      const mappingResult = dataMapper.mapMoviesData(apiMovies.data);
      console.log(`✅ 映射完成: 成功${mappingResult.successCount}部，失败${mappingResult.errorCount}部`);
      
      if (mappingResult.success.length > 0) {
        // 3. 数据验证
        console.log('🔍 进行数据验证...');
        const validationResult = validationService.validateMoviesData(mappingResult.success);
        console.log(`✅ 验证完成: 有效${validationResult.validCount}部，无效${validationResult.invalidCount}部`);
        
        // 4. 生成质量报告
        const qualityReport = validationService.generateQualityReport(validationResult, 'movies');
        console.log('📋 数据质量报告:');
        console.log(`  - 数据来源: ${adaptiveApi.getConfig().currentServiceType} API`);
        console.log(`  - 总数: ${qualityReport.overview.total}`);
        console.log(`  - 有效率: ${qualityReport.overview.validRate}%`);
        console.log(`  - 重复率: ${qualityReport.overview.duplicateRate}%`);
        
        // 显示第一个有效数据的结构
        if (validationResult.valid.length > 0) {
          const firstValidMovie = validationResult.valid[0].data;
          console.log('📋 第一个有效影片数据结构:');
          console.log(`  - ID: ${firstValidMovie.movie_id}`);
          console.log(`  - 标题: ${firstValidMovie.title}`);
          console.log(`  - 发布日期: ${firstValidMovie.release_date}`);
          console.log(`  - 时长: ${firstValidMovie.duration}`);
          console.log(`  - 状态: ${firstValidMovie.status}`);
          console.log(`  - 是否无码: ${firstValidMovie.isUncensored ? '是' : '否'}`);
          console.log(`  - 演员数量: ${firstValidMovie.stars?.length || 0}`);
          console.log(`  - 分类数量: ${firstValidMovie.genres?.length || 0}`);
          console.log(`  - 磁力链接: ${firstValidMovie.magnets?.length || 0}`);
        }
      }
    }
  } catch (error) {
    console.log('❌ 影片数据流程测试失败:', error.message);
  }
  console.log('');
  
  // 测试2: 完整的数据流程 - 演员数据
  console.log('🔍 测试2: 完整的演员数据流程（API → 映射 → 验证）');
  try {
    // 1. 从API获取数据
    console.log('📡 从API获取演员数据...');
    const apiStars = await adaptiveApi.getActors({ limit: 5 });
    console.log(`✅ API返回 ${apiStars.data?.length || 0} 位演员`);
    
    if (apiStars.data && apiStars.data.length > 0) {
      // 2. 数据映射
      console.log('🔄 进行数据映射...');
      const mappingResult = dataMapper.mapStarsData(apiStars.data);
      console.log(`✅ 映射完成: 成功${mappingResult.successCount}位，失败${mappingResult.errorCount}位`);
      
      if (mappingResult.success.length > 0) {
        // 3. 数据验证
        console.log('🔍 进行数据验证...');
        const validationResult = validationService.validateStarsData(mappingResult.success);
        console.log(`✅ 验证完成: 有效${validationResult.validCount}位，无效${validationResult.invalidCount}位`);
        
        // 4. 生成质量报告
        const qualityReport = validationService.generateQualityReport(validationResult, 'stars');
        console.log('📋 演员数据质量报告:');
        console.log(`  - 有效率: ${qualityReport.overview.validRate}%`);
        console.log(`  - 错误总数: ${qualityReport.errors.totalErrorCount}`);
        console.log(`  - 警告总数: ${qualityReport.warnings.totalWarningCount}`);
        
        // 显示第一个有效演员的数据结构
        if (validationResult.valid.length > 0) {
          const firstValidStar = validationResult.valid[0].data;
          console.log('📋 第一个有效演员数据结构:');
          console.log(`  - ID: ${firstValidStar.star_id}`);
          console.log(`  - 姓名: ${firstValidStar.name}`);
          console.log(`  - 年龄: ${firstValidStar.age || '未知'}`);
          console.log(`  - 身高: ${firstValidStar.height || '未知'}cm`);
          console.log(`  - 生日: ${firstValidStar.birthday || '未知'}`);
          console.log(`  - 出生地: ${firstValidStar.birthplace || '未知'}`);
        }
      }
    }
  } catch (error) {
    console.log('❌ 演员数据流程测试失败:', error.message);
  }
  console.log('');
  
  // 测试3: 分类数据流程
  console.log('🔍 测试3: 完整的分类数据流程（API → 映射 → 验证）');
  try {
    // 1. 从API获取数据
    console.log('📡 从API获取分类数据...');
    const apiGenres = await adaptiveApi.getGenres();
    console.log(`✅ API返回 ${apiGenres.data?.length || 0} 个分类`);
    
    if (apiGenres.data && apiGenres.data.length > 0) {
      // 2. 数据映射和验证
      const mappedGenres = [];
      const validGenres = [];
      
      for (const genre of apiGenres.data) {
        try {
          const mappedGenre = dataMapper.mapGenreData(genre);
          const validation = validationService.validateGenreData(mappedGenre);
          
          if (validation.isValid) {
            validGenres.push(validation.data);
          }
          mappedGenres.push(mappedGenre);
        } catch (error) {
          console.log(`⚠️ 分类映射失败: ${error.message}`);
        }
      }
      
      console.log(`✅ 分类处理完成: 映射${mappedGenres.length}个，有效${validGenres.length}个`);
      
      if (validGenres.length > 0) {
        console.log('📋 有效分类列表:');
        validGenres.slice(0, 5).forEach(genre => {
          console.log(`  - ${genre.name}`);
        });
      }
    }
  } catch (error) {
    console.log('❌ 分类数据流程测试失败:', error.message);
  }
  console.log('');
  
  // 测试4: 性能测试
  console.log('🔍 测试4: 数据处理性能测试');
  try {
    const startTime = Date.now();
    
    // 并发获取多种数据
    const [moviesData, starsData, genresData] = await Promise.all([
      adaptiveApi.getLatestMovies({ limit: 10 }),
      adaptiveApi.getActors({ limit: 10 }),
      adaptiveApi.getGenres()
    ]);
    
    const apiTime = Date.now() - startTime;
    console.log(`📡 API调用耗时: ${apiTime}ms`);
    
    // 数据映射性能测试
    const mappingStartTime = Date.now();
    const movieMappingResult = dataMapper.mapMoviesData(moviesData.data || []);
    const starMappingResult = dataMapper.mapStarsData(starsData.data || []);
    const mappingTime = Date.now() - mappingStartTime;
    console.log(`🔄 数据映射耗时: ${mappingTime}ms`);
    
    // 数据验证性能测试
    const validationStartTime = Date.now();
    const movieValidationResult = validationService.validateMoviesData(movieMappingResult.success);
    const starValidationResult = validationService.validateStarsData(starMappingResult.success);
    const validationTime = Date.now() - validationStartTime;
    console.log(`🔍 数据验证耗时: ${validationTime}ms`);
    
    const totalTime = Date.now() - startTime;
    console.log(`⏱️ 总处理时间: ${totalTime}ms`);
    
    // 性能统计
    const totalProcessed = (movieMappingResult.successCount || 0) + (starMappingResult.successCount || 0);
    const avgProcessingTime = totalProcessed > 0 ? (totalTime / totalProcessed).toFixed(2) : 0;
    console.log(`📊 处理统计:`);
    console.log(`  - 总处理数据: ${totalProcessed}条`);
    console.log(`  - 平均处理时间: ${avgProcessingTime}ms/条`);
    console.log(`  - 影片有效率: ${movieValidationResult.total > 0 ? (movieValidationResult.validCount / movieValidationResult.total * 100).toFixed(2) : 0}%`);
    console.log(`  - 演员有效率: ${starValidationResult.total > 0 ? (starValidationResult.validCount / starValidationResult.total * 100).toFixed(2) : 0}%`);
    
  } catch (error) {
    console.log('❌ 性能测试失败:', error.message);
  }
  console.log('');
  
  // 测试5: 错误处理测试
  console.log('🔍 测试5: 错误处理测试');
  try {
    // 测试无效数据的处理
    const invalidData = [
      { id: null, title: null }, // 无效影片数据
      { invalid: 'data' }, // 完全无效的数据
      null, // null数据
      undefined // undefined数据
    ];
    
    console.log('🔄 测试无效数据映射...');
    let mappingErrors = 0;
    for (const data of invalidData) {
      try {
        if (data !== null && data !== undefined) {
          dataMapper.mapMovieData(data);
        }
      } catch (error) {
        mappingErrors++;
      }
    }
    console.log(`✅ 映射错误处理: ${mappingErrors}个错误被正确捕获`);
    
    // 测试验证服务的错误处理
    console.log('🔍 测试验证错误处理...');
    try {
      validationService.validateMoviesData('invalid input'); // 非数组输入
    } catch (error) {
      console.log('✅ 验证服务错误处理正常:', error.message);
    }
    
  } catch (error) {
    console.log('❌ 错误处理测试失败:', error.message);
  }
  console.log('');
  
  console.log('🎉 API服务、数据映射和验证集成测试完成！');
  
  // 最终总结
  console.log('\n📋 集成测试总结:');
  console.log(`- API服务类型: ${adaptiveApi.getConfig().currentServiceType}`);
  console.log(`- 数据映射功能: 正常`);
  console.log(`- 数据验证功能: 正常`);
  console.log(`- 错误处理机制: 正常`);
  console.log(`- 性能表现: 良好`);
}

// 运行测试
if (require.main === module) {
  testIntegrationMappingValidation().catch(error => {
    console.error('集成测试过程中发生未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = { testIntegrationMappingValidation };
