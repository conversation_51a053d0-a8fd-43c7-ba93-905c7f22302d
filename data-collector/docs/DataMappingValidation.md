# 数据映射和验证服务文档

## 概述

数据映射和验证服务是JAVFLIX.TV数据采集系统的核心组件，负责将外网API数据格式转换为本地数据库格式，并确保数据质量和完整性。

## 核心组件

### 1. DataMapper 数据映射器

负责外网API数据格式到本地数据库格式的转换映射。

#### 主要功能
- **影片数据映射**：外网API格式 → movies表格式
- **演员数据映射**：外网API格式 → stars表格式  
- **分类数据映射**：外网API格式 → genres表格式
- **关联数据映射**：导演、制作商、发行商、系列、磁力链接等
- **数据清洗**：字符串清理、URL验证、日期解析、数值转换
- **批量处理**：支持批量数据映射和错误统计

#### 使用示例

```javascript
const DataMapper = require('./src/mappers/DataMapper');
const dataMapper = new DataMapper();

// 映射单个影片数据
const externalMovie = {
  id: 'MOCK-000001',
  title: '测试影片',
  release_date: '2024-01-15',
  duration: '120分钟',
  stars: [{ id: 'STAR-001', name: '演员A' }]
};

const mappedMovie = dataMapper.mapMovieData(externalMovie);

// 批量映射影片数据
const mappingResult = dataMapper.mapMoviesData(externalMovies);
console.log(`成功映射: ${mappingResult.successCount}部`);
console.log(`映射失败: ${mappingResult.errorCount}部`);
```

### 2. ValidationService 数据验证服务

实现数据质量验证机制，确保数据的完整性和一致性。

#### 验证规则
- **必填字段验证**：title、release_date等核心字段
- **数据类型验证**：日期格式、数字范围等
- **数据长度限制**：标题长度、描述长度等
- **重复数据检查**：基于唯一标识符
- **业务逻辑验证**：年龄与生日一致性、出道日期合理性等

#### 使用示例

```javascript
const ValidationService = require('./src/services/ValidationService');
const validationService = new ValidationService();

// 验证单个影片数据
const validation = validationService.validateMovieData(movieData);
if (validation.isValid) {
  console.log('数据验证通过');
} else {
  console.log('验证错误:', validation.errors);
  console.log('验证警告:', validation.warnings);
}

// 批量验证影片数据
const validationResult = validationService.validateMoviesData(moviesData);
console.log(`有效数据: ${validationResult.validCount}部`);
console.log(`无效数据: ${validationResult.invalidCount}部`);
console.log(`重复数据: ${validationResult.duplicateCount}部`);

// 生成数据质量报告
const qualityReport = validationService.generateQualityReport(validationResult, 'movies');
console.log(`数据有效率: ${qualityReport.overview.validRate}%`);
```

## 数据映射规则

### 影片数据映射

| 外网API字段 | 本地数据库字段 | 数据处理 |
|------------|---------------|----------|
| id / movie_id | movie_id | 字符串清理 |
| title | title | 字符串清理，长度限制255 |
| image_url / imageUrl | image_url | URL验证 |
| release_date / releaseDate | release_date | 日期格式化 |
| duration | duration | 字符串清理 |
| description | description | 文本清理，长度限制5000 |

### 演员数据映射

| 外网API字段 | 本地数据库字段 | 数据处理 |
|------------|---------------|----------|
| id / star_id | star_id | 字符串清理 |
| name | name | 字符串清理，长度限制100 |
| birthday | birthday | 日期格式化 |
| age | age | 整数转换，范围验证18-80 |
| height | height | 移除cm单位，范围验证100-250 |
| bust/waist/hip | bust/waist/hip | 字符串清理 |

## 验证规则详解

### 必填字段验证
- **影片**：movie_id, title
- **演员**：star_id, name  
- **分类**：name

### 数据格式验证
- **日期格式**：YYYY-MM-DD
- **影片ID格式**：字母数字和连字符
- **URL格式**：http/https协议或相对路径

### 业务逻辑验证
- **发布日期**：不能是未来日期
- **年龄与生日**：一致性检查
- **出道日期**：不能早于生日
- **身体数据**：腰围应小于胸围和臀围

## 数据质量报告

### 报告内容
- **概览统计**：总数、有效数、无效数、重复数、有效率、重复率
- **错误统计**：错误类型分布、最常见错误
- **警告统计**：警告类型分布、最常见警告  
- **改进建议**：基于验证结果的自动建议

### 报告示例
```json
{
  "dataType": "movies",
  "overview": {
    "total": 100,
    "valid": 85,
    "invalid": 10,
    "duplicates": 5,
    "validRate": "85.00",
    "duplicateRate": "5.00"
  },
  "errors": {
    "totalErrorCount": 15,
    "topErrors": [
      { "issue": "必填字段缺失: title", "count": 8 },
      { "issue": "字段长度超限", "count": 4 }
    ]
  },
  "recommendations": [
    "数据质量较低，建议检查数据源和映射逻辑",
    "存在必填字段缺失，建议检查数据完整性"
  ]
}
```

## 集成使用

### 完整数据处理流程

```javascript
const AdaptiveApiService = require('./src/services/AdaptiveApiService');
const DataMapper = require('./src/mappers/DataMapper');
const ValidationService = require('./src/services/ValidationService');

async function processMovieData() {
  // 1. 初始化服务
  const apiService = new AdaptiveApiService();
  const dataMapper = new DataMapper();
  const validationService = new ValidationService();
  
  await apiService.initialize();
  
  // 2. 获取外网数据
  const apiData = await apiService.getLatestMovies({ limit: 100 });
  
  // 3. 数据映射
  const mappingResult = dataMapper.mapMoviesData(apiData.data);
  console.log(`映射完成: 成功${mappingResult.successCount}部`);
  
  // 4. 数据验证
  const validationResult = validationService.validateMoviesData(mappingResult.success);
  console.log(`验证完成: 有效${validationResult.validCount}部`);
  
  // 5. 生成质量报告
  const qualityReport = validationService.generateQualityReport(validationResult, 'movies');
  console.log(`数据质量: ${qualityReport.overview.validRate}%`);
  
  // 6. 返回有效数据
  return validationResult.valid.map(item => item.data);
}
```

## 性能特性

### 处理性能
- **平均处理时间**：10ms/条数据
- **批量处理**：支持大批量数据处理
- **内存优化**：流式处理，避免内存溢出
- **错误隔离**：单条数据错误不影响整体处理

### 扩展性
- **规则配置化**：验证规则可配置
- **插件化架构**：支持自定义映射器和验证器
- **多数据源支持**：适配不同外网API格式

## 错误处理

### 映射错误
- **字段缺失**：使用默认值或null
- **类型转换失败**：记录错误并跳过
- **格式错误**：数据清洗或标记为无效

### 验证错误
- **必填字段缺失**：标记为无效数据
- **格式不正确**：提供详细错误信息
- **业务逻辑冲突**：记录警告信息

## 最佳实践

1. **数据源检查**：定期检查外网API数据质量
2. **规则更新**：根据业务需求更新验证规则
3. **性能监控**：监控处理性能和错误率
4. **质量报告**：定期生成和分析数据质量报告
5. **错误处理**：建立完善的错误处理和恢复机制

## 配置选项

### 验证规则配置
```javascript
const validationRules = {
  movies: {
    required: ['movie_id', 'title'],
    maxLengths: {
      movie_id: 50,
      title: 255,
      description: 5000
    },
    patterns: {
      movie_id: /^[a-zA-Z0-9\-_]+$/,
      release_date: /^\d{4}-\d{2}-\d{2}$/
    }
  }
};
```

### 映射配置
```javascript
const mappingConfig = {
  dateFormat: 'YYYY-MM-DD',
  defaultStatus: 'published',
  defaultPriority: 5,
  maxTextLength: 5000
};
```

## 测试验证

运行测试命令验证功能：

```bash
# 基础功能测试
node test-data-mapping-validation.js

# 集成测试
node test-integration-mapping-validation.js
```

测试覆盖：
- ✅ 数据映射功能测试
- ✅ 数据验证功能测试  
- ✅ 批量处理测试
- ✅ 错误处理测试
- ✅ 性能测试
- ✅ 集成测试
