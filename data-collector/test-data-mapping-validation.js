const winston = require('winston');
const DataMapper = require('./src/mappers/DataMapper');
const ValidationService = require('./src/services/ValidationService');

// 配置winston日志
winston.configure({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

/**
 * 测试数据映射和验证服务
 */
async function testDataMappingAndValidation() {
  console.log('🚀 开始测试数据映射和验证服务...\n');
  
  const dataMapper = new DataMapper();
  const validationService = new ValidationService();
  
  // 测试数据
  const testExternalMovies = [
    {
      id: 'MOCK-000001',
      title: '测试影片标题 1',
      image_url: 'https://example.com/images/001.jpg',
      release_date: '2024-01-15',
      duration: '120分钟',
      description: '这是一个测试影片的详细描述信息，包含足够的内容来通过验证。',
      director: { id: 'DIR-001', name: '导演A' },
      producer: { id: 'PROD-001', name: '制作商A' },
      stars: [
        {
          id: 'STAR-001',
          name: '演员A',
          birthday: '1995-05-15',
          age: 29,
          height: '165cm',
          bust: '88cm',
          waist: '60cm',
          hip: '90cm'
        }
      ],
      genres: ['动作', '剧情'],
      magnets: [
        {
          id: 'MAG-001',
          link: 'magnet:?xt=urn:btih:example1',
          title: '高清版本',
          size: '2.5GB',
          is_hd: true,
          has_subtitle: true
        }
      ]
    },
    {
      id: 'MOCK-000002',
      title: '测试影片标题 2',
      image_url: 'https://example.com/images/002.jpg',
      release_date: '2024-02-20',
      duration: '90分钟',
      description: '另一个测试影片的描述。',
      stars: [
        {
          id: 'STAR-002',
          name: '演员B',
          birthday: '1992-08-10',
          age: 32,
          height: '160',
          bust: '85',
          waist: '58',
          hip: '88'
        }
      ],
      genres: ['喜剧']
    },
    // 测试无效数据
    {
      id: '', // 缺失必填字段
      title: '', // 缺失必填字段
      release_date: '2025-12-31', // 未来日期
      duration: 'invalid duration', // 格式错误
      stars: [
        {
          id: 'STAR-003',
          name: '演员C',
          age: 15, // 年龄过小
          height: 300 // 身高过大
        }
      ]
    },
    // 测试重复数据
    {
      id: 'MOCK-000001', // 重复ID
      title: '重复的影片标题',
      description: '重复数据测试'
    }
  ];
  
  const testExternalStars = [
    {
      id: 'STAR-001',
      name: '演员A',
      birthday: '1995-05-15',
      age: 29,
      height: '165cm',
      birthplace: '日本',
      debut_date: '2018-01-01',
      bust: '88cm',
      waist: '60cm',
      hip: '90cm',
      cup_size: 'C'
    },
    {
      id: 'STAR-002',
      name: '演员B',
      birthday: '1992-08-10',
      age: 32,
      height: '160',
      bust: '85',
      waist: '58',
      hip: '88'
    },
    // 无效数据测试
    {
      id: '', // 缺失必填字段
      name: '', // 缺失必填字段
      age: 100, // 年龄过大
      height: 50, // 身高过小
      debut_date: '1990-01-01', // 出道日期早于生日
      birthday: '1995-01-01'
    }
  ];
  
  // 测试1: 影片数据映射
  console.log('🔍 测试1: 影片数据映射');
  try {
    const mappingResult = dataMapper.mapMoviesData(testExternalMovies);
    console.log('✅ 影片数据映射完成');
    console.log(`📊 映射结果: 成功${mappingResult.successCount}部，失败${mappingResult.errorCount}部`);
    
    if (mappingResult.success.length > 0) {
      console.log('📋 首个映射成功的影片:');
      console.log(JSON.stringify(mappingResult.success[0], null, 2));
    }
    
    if (mappingResult.errors.length > 0) {
      console.log('❌ 映射错误:');
      mappingResult.errors.forEach(error => {
        console.log(`  - 索引${error.index}: ${error.error}`);
      });
    }
  } catch (error) {
    console.log('❌ 影片数据映射失败:', error.message);
  }
  console.log('');
  
  // 测试2: 演员数据映射
  console.log('🔍 测试2: 演员数据映射');
  try {
    const mappingResult = dataMapper.mapStarsData(testExternalStars);
    console.log('✅ 演员数据映射完成');
    console.log(`📊 映射结果: 成功${mappingResult.successCount}位，失败${mappingResult.errorCount}位`);
    
    if (mappingResult.success.length > 0) {
      console.log('📋 首个映射成功的演员:');
      console.log(JSON.stringify(mappingResult.success[0], null, 2));
    }
  } catch (error) {
    console.log('❌ 演员数据映射失败:', error.message);
  }
  console.log('');
  
  // 测试3: 影片数据验证
  console.log('🔍 测试3: 影片数据验证');
  try {
    const mappedMovies = dataMapper.mapMoviesData(testExternalMovies).success;
    const validationResult = validationService.validateMoviesData(mappedMovies);
    
    console.log('✅ 影片数据验证完成');
    console.log(`📊 验证结果: 总数${validationResult.total}, 有效${validationResult.validCount}, 无效${validationResult.invalidCount}, 重复${validationResult.duplicateCount}`);
    
    // 显示验证详情
    if (validationResult.invalid.length > 0) {
      console.log('❌ 验证失败的影片:');
      validationResult.invalid.forEach(item => {
        console.log(`  - 索引${item.index}: ${item.errors.join(', ')}`);
      });
    }
    
    if (validationResult.duplicates.length > 0) {
      console.log('🔄 重复的影片:');
      validationResult.duplicates.forEach(item => {
        console.log(`  - 索引${item.index}: ${item.movie_id}`);
      });
    }
    
    // 生成质量报告
    const qualityReport = validationService.generateQualityReport(validationResult, 'movies');
    console.log('📋 数据质量报告:');
    console.log(`  - 有效率: ${qualityReport.overview.validRate}%`);
    console.log(`  - 重复率: ${qualityReport.overview.duplicateRate}%`);
    console.log(`  - 错误总数: ${qualityReport.errors.totalErrorCount}`);
    console.log(`  - 警告总数: ${qualityReport.warnings.totalWarningCount}`);
    
    if (qualityReport.recommendations.length > 0) {
      console.log('💡 改进建议:');
      qualityReport.recommendations.forEach(rec => {
        console.log(`  - ${rec}`);
      });
    }
    
  } catch (error) {
    console.log('❌ 影片数据验证失败:', error.message);
  }
  console.log('');
  
  // 测试4: 演员数据验证
  console.log('🔍 测试4: 演员数据验证');
  try {
    const mappedStars = dataMapper.mapStarsData(testExternalStars).success;
    const validationResult = validationService.validateStarsData(mappedStars);
    
    console.log('✅ 演员数据验证完成');
    console.log(`📊 验证结果: 总数${validationResult.total}, 有效${validationResult.validCount}, 无效${validationResult.invalidCount}, 重复${validationResult.duplicateCount}`);
    
    // 生成质量报告
    const qualityReport = validationService.generateQualityReport(validationResult, 'stars');
    console.log('📋 演员数据质量报告:');
    console.log(`  - 有效率: ${qualityReport.overview.validRate}%`);
    console.log(`  - 错误总数: ${qualityReport.errors.totalErrorCount}`);
    console.log(`  - 警告总数: ${qualityReport.warnings.totalWarningCount}`);
    
  } catch (error) {
    console.log('❌ 演员数据验证失败:', error.message);
  }
  console.log('');
  
  // 测试5: 单个数据验证
  console.log('🔍 测试5: 单个数据验证');
  try {
    const singleMovie = dataMapper.mapMovieData(testExternalMovies[0]);
    const singleValidation = validationService.validateMovieData(singleMovie);
    
    console.log('✅ 单个影片验证完成');
    console.log(`📊 验证结果: ${singleValidation.isValid ? '有效' : '无效'}`);
    console.log(`📋 错误数量: ${singleValidation.errors.length}`);
    console.log(`📋 警告数量: ${singleValidation.warnings.length}`);
    
    if (singleValidation.warnings.length > 0) {
      console.log('⚠️ 警告信息:');
      singleValidation.warnings.forEach(warning => {
        console.log(`  - ${warning}`);
      });
    }
    
  } catch (error) {
    console.log('❌ 单个数据验证失败:', error.message);
  }
  console.log('');
  
  console.log('🎉 数据映射和验证服务测试完成！');
}

// 运行测试
if (require.main === module) {
  testDataMappingAndValidation().catch(error => {
    console.error('测试过程中发生未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = { testDataMappingAndValidation };
