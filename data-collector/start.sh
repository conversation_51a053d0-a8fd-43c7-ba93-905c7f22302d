#!/bin/bash

# JAVFLIX 数据采集服务启动脚本

echo "🚀 启动 JAVFLIX 数据采集服务..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm环境
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 进入项目目录
cd "$(dirname "$0")"

# 创建日志目录
mkdir -p logs

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

# 检查数据库连接
echo "🔍 检查数据库连接..."
if ! node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => {
    console.log('✅ 数据库连接成功');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  });
"; then
    echo "❌ 数据库连接失败，请检查配置"
    exit 1
fi

# 检查外网API连接
echo "🌐 检查外网API连接..."
if ! curl -s --connect-timeout 10 http://188.68.60.179:8081/health > /dev/null; then
    echo "⚠️  外网API连接异常，但继续启动服务"
fi

# 启动服务
echo "✅ 启动数据采集服务..."

# 检查是否传入了参数
if [ "$1" = "--now" ]; then
    echo "🔄 立即执行一次数据采集..."
    npm run collect-now
elif [ "$1" = "--dev" ]; then
    echo "🔧 开发模式启动..."
    npm run dev
else
    echo "⏰ 定时采集模式启动..."
    npm start
fi
