const winston = require('winston');
const AdaptiveApiService = require('./src/services/AdaptiveApiService');

// 配置winston日志
winston.configure({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

/**
 * 测试自适应API服务
 */
async function testAdaptiveApiService() {
  console.log('🚀 开始测试自适应API服务...\n');
  
  const adaptiveApi = new AdaptiveApiService();
  
  // 测试1: 初始化和健康检查
  console.log('🔍 测试1: 服务初始化和健康检查');
  try {
    await adaptiveApi.initialize();
    const healthStatus = await adaptiveApi.healthCheck();
    console.log('✅ 健康检查结果:', JSON.stringify(healthStatus, null, 2));
    
    const config = adaptiveApi.getConfig();
    console.log('📋 服务配置:', JSON.stringify(config, null, 2));
  } catch (error) {
    console.log('❌ 初始化失败:', error.message);
  }
  console.log('');
  
  // 测试2: 获取最新影片
  console.log('🔍 测试2: 获取最新影片');
  try {
    const movies = await adaptiveApi.getLatestMovies({ page: 1, limit: 5 });
    console.log('✅ 最新影片获取成功');
    console.log(`📊 返回数据: ${movies.data?.length || 0}条影片`);
    if (movies.data && movies.data.length > 0) {
      console.log('📋 首个影片示例:', JSON.stringify(movies.data[0], null, 2));
    }
    if (movies.pagination) {
      console.log('📄 分页信息:', JSON.stringify(movies.pagination, null, 2));
    }
  } catch (error) {
    console.log('❌ 获取最新影片失败:', error.message);
  }
  console.log('');
  
  // 测试3: 获取热门影片
  console.log('🔍 测试3: 获取热门影片');
  try {
    const movies = await adaptiveApi.getPopularMovies({ page: 1, limit: 3 });
    console.log('✅ 热门影片获取成功');
    console.log(`📊 返回数据: ${movies.data?.length || 0}条影片`);
    if (movies.data && movies.data.length > 0) {
      const popularMovie = movies.data[0];
      console.log(`📋 热门影片示例: ${popularMovie.title} (观看次数: ${popularMovie.view_count})`);
    }
  } catch (error) {
    console.log('❌ 获取热门影片失败:', error.message);
  }
  console.log('');
  
  // 测试4: 获取影片详情
  console.log('🔍 测试4: 获取影片详情');
  try {
    const movieDetail = await adaptiveApi.getMovieDetail('MOCK-000001');
    console.log('✅ 影片详情获取成功');
    console.log(`📋 影片信息: ${movieDetail.data?.title}`);
    console.log(`🎭 演员数量: ${movieDetail.data?.stars?.length || 0}人`);
    console.log(`🏷️ 分类数量: ${movieDetail.data?.genres?.length || 0}个`);
    console.log(`🧲 磁力链接: ${movieDetail.data?.magnets?.length || 0}个`);
  } catch (error) {
    console.log('❌ 获取影片详情失败:', error.message);
  }
  console.log('');
  
  // 测试5: 获取演员列表
  console.log('🔍 测试5: 获取演员列表');
  try {
    const actors = await adaptiveApi.getActors({ page: 1, limit: 5 });
    console.log('✅ 演员列表获取成功');
    console.log(`📊 返回数据: ${actors.data?.length || 0}位演员`);
    if (actors.data && actors.data.length > 0) {
      const actor = actors.data[0];
      console.log(`👤 演员示例: ${actor.name} (${actor.age}岁, 作品${actor.movie_count}部)`);
    }
  } catch (error) {
    console.log('❌ 获取演员列表失败:', error.message);
  }
  console.log('');
  
  // 测试6: 获取分类列表
  console.log('🔍 测试6: 获取分类列表');
  try {
    const genres = await adaptiveApi.getGenres();
    console.log('✅ 分类列表获取成功');
    console.log(`📊 返回数据: ${genres.data?.length || 0}个分类`);
    if (genres.data && genres.data.length > 0) {
      console.log('🏷️ 分类列表:');
      genres.data.forEach(genre => {
        console.log(`  - ${genre.name}: ${genre.count}部影片`);
      });
    }
  } catch (error) {
    console.log('❌ 获取分类列表失败:', error.message);
  }
  console.log('');
  
  // 测试7: 获取服务器统计
  console.log('🔍 测试7: 获取服务器统计');
  try {
    const stats = await adaptiveApi.getServerStats();
    console.log('✅ 服务器统计获取成功');
    console.log('📊 统计信息:', JSON.stringify(stats.data, null, 2));
  } catch (error) {
    console.log('❌ 获取服务器统计失败:', error.message);
  }
  console.log('');
  
  // 测试8: 服务切换功能
  console.log('🔍 测试8: 服务切换功能');
  try {
    console.log('当前服务配置:', adaptiveApi.getConfig().currentServiceType);
    
    // 切换到模拟服务
    adaptiveApi.switchToMockService();
    console.log('✅ 成功切换到模拟服务');
    
    // 测试模拟服务
    const mockMovies = await adaptiveApi.getLatestMovies({ limit: 2 });
    console.log(`📊 模拟服务返回: ${mockMovies.data?.length || 0}条影片`);
    
    console.log('最终服务配置:', adaptiveApi.getConfig().currentServiceType);
  } catch (error) {
    console.log('❌ 服务切换测试失败:', error.message);
  }
  console.log('');
  
  // 测试9: 性能测试
  console.log('🔍 测试9: 性能测试');
  try {
    const startTime = Date.now();
    
    // 并发请求测试
    const promises = [
      adaptiveApi.getLatestMovies({ limit: 3 }),
      adaptiveApi.getPopularMovies({ limit: 3 }),
      adaptiveApi.getActors({ limit: 3 }),
      adaptiveApi.getGenres()
    ];
    
    const results = await Promise.all(promises);
    const endTime = Date.now();
    
    console.log('✅ 并发请求测试完成');
    console.log(`⏱️ 总耗时: ${endTime - startTime}ms`);
    console.log(`📊 请求结果: ${results.length}个API调用成功`);
    
    // 计算平均响应时间
    const avgTime = (endTime - startTime) / results.length;
    console.log(`📈 平均响应时间: ${avgTime.toFixed(2)}ms`);
    
  } catch (error) {
    console.log('❌ 性能测试失败:', error.message);
  }
  console.log('');
  
  console.log('🎉 自适应API服务测试完成！');
  
  // 显示最终状态
  console.log('\n📋 最终服务状态:');
  const finalConfig = adaptiveApi.getConfig();
  console.log(`- 服务类型: ${finalConfig.currentServiceType}`);
  console.log(`- 基础URL: ${finalConfig.baseURL}`);
  console.log(`- 自适应模式: ${finalConfig.adaptiveService ? '启用' : '禁用'}`);
  console.log(`- 使用真实API: ${finalConfig.useRealApi ? '是' : '否'}`);
}

// 运行测试
if (require.main === module) {
  testAdaptiveApiService().catch(error => {
    console.error('测试过程中发生未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = { testAdaptiveApiService };
