// 加载环境变量
require('dotenv').config();

const winston = require('winston');
const cron = require('node-cron');
const AdaptiveApiService = require('./AdaptiveApiService');
const DataMapper = require('../mappers/DataMapper');
const ValidationService = require('./ValidationService');
const SyncEngine = require('./SyncEngine');

/**
 * 数据采集服务主类
 * 协调所有组件进行完整的数据采集流程
 */
class DataCollectionService {
  constructor() {
    // 初始化所有组件
    this.apiService = new AdaptiveApiService();
    this.dataMapper = new DataMapper();
    this.validationService = new ValidationService();
    this.syncEngine = new SyncEngine(this.apiService, this.dataMapper, this.validationService);
    
    // 采集配置
    this.config = {
      collectionInterval: process.env.COLLECTION_INTERVAL || '0 */1 * * *', // 每小时
      batchSizes: {
        movies: parseInt(process.env.BATCH_SIZE_MOVIES || '50'),
        actors: parseInt(process.env.BATCH_SIZE_ACTORS || '30'),
        genres: parseInt(process.env.BATCH_SIZE_GENRES || '20')
      },
      autoPublish: process.env.AUTO_PUBLISH === 'true',
      skipDuplicates: process.env.SKIP_DUPLICATES !== 'false',
      validateData: process.env.VALIDATE_DATA !== 'false'
    };
    
    this.isRunning = false;
    this.cronJob = null;
    
    winston.info('DataCollectionService 初始化完成', {
      collectionInterval: this.config.collectionInterval,
      batchSizes: this.config.batchSizes
    });
  }
  
  /**
   * 初始化服务
   */
  async initialize() {
    winston.info('正在初始化数据采集服务...');
    
    try {
      // 初始化API服务
      await this.apiService.initialize();
      
      // 检查同步状态
      const syncStats = await this.syncEngine.getSyncStatistics();
      winston.info('当前同步状态:', {
        entities: syncStats.map(s => ({
          type: s.entity_type,
          status: s.status,
          lastSync: s.last_sync_time,
          totalSynced: s.total_synced,
          successRate: s.success_rate + '%'
        }))
      });
      
      winston.info('✅ 数据采集服务初始化完成');
      return true;
      
    } catch (error) {
      winston.error('❌ 数据采集服务初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 启动定时采集任务
   */
  startScheduledCollection() {
    if (this.cronJob) {
      winston.warn('定时采集任务已在运行中');
      return;
    }
    
    winston.info(`启动定时采集任务: ${this.config.collectionInterval}`);
    
    this.cronJob = cron.schedule(this.config.collectionInterval, async () => {
      if (!this.isRunning) {
        winston.info('🕐 定时采集任务触发');
        await this.performIncrementalCollection();
      } else {
        winston.warn('⚠️ 上次采集任务仍在运行中，跳过本次执行');
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });
    
    this.cronJob.start();
    winston.info('✅ 定时采集任务已启动');
  }
  
  /**
   * 停止定时采集任务
   */
  stopScheduledCollection() {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      winston.info('⏹️ 定时采集任务已停止');
    }
  }
  
  /**
   * 执行增量数据采集
   */
  async performIncrementalCollection() {
    if (this.isRunning) {
      winston.warn('采集任务已在运行中');
      return;
    }
    
    this.isRunning = true;
    const startTime = Date.now();
    
    winston.info('🚀 开始增量数据采集');
    
    try {
      const results = {
        movies: null,
        actors: null,
        genres: null,
        totalTime: 0,
        success: true
      };
      
      // 1. 采集影片数据
      winston.info('📽️ 开始采集影片数据...');
      try {
        results.movies = await this.syncEngine.performIncrementalSync('movies', {
          batchSize: this.config.batchSizes.movies
        });
        winston.info(`✅ 影片采集完成: 成功${results.movies.successCount}, 失败${results.movies.errorCount}`);
      } catch (error) {
        winston.error('❌ 影片采集失败:', error);
        results.success = false;
      }
      
      // 2. 采集演员数据
      winston.info('👥 开始采集演员数据...');
      try {
        results.actors = await this.syncEngine.performIncrementalSync('actors', {
          batchSize: this.config.batchSizes.actors
        });
        winston.info(`✅ 演员采集完成: 成功${results.actors.successCount}, 失败${results.actors.errorCount}`);
      } catch (error) {
        winston.error('❌ 演员采集失败:', error);
        results.success = false;
      }
      
      // 3. 采集分类数据
      winston.info('🏷️ 开始采集分类数据...');
      try {
        results.genres = await this.syncEngine.performIncrementalSync('genres', {
          batchSize: this.config.batchSizes.genres
        });
        winston.info(`✅ 分类采集完成: 成功${results.genres.successCount}, 失败${results.genres.errorCount}`);
      } catch (error) {
        winston.error('❌ 分类采集失败:', error);
        results.success = false;
      }
      
      results.totalTime = Date.now() - startTime;
      
      // 4. 生成采集报告
      const report = this.generateCollectionReport(results);
      winston.info('📊 采集报告:', report);
      
      // 5. 清理旧日志（每天执行一次）
      const now = new Date();
      if (now.getHours() === 2 && now.getMinutes() < 10) { // 凌晨2点左右
        winston.info('🧹 开始清理旧同步日志...');
        const deletedCount = await this.syncEngine.cleanupOldSyncLogs(30);
        winston.info(`✅ 清理完成，删除${deletedCount}条旧日志`);
      }
      
      winston.info(`🎉 增量数据采集完成，总耗时: ${results.totalTime}ms`);
      return results;
      
    } catch (error) {
      winston.error('❌ 增量数据采集失败:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }
  
  /**
   * 执行全量数据采集（初始化用）
   */
  async performFullCollection() {
    if (this.isRunning) {
      winston.warn('采集任务已在运行中');
      return;
    }
    
    this.isRunning = true;
    const startTime = Date.now();
    
    winston.info('🚀 开始全量数据采集（初始化）');
    
    try {
      const results = {
        movies: null,
        actors: null,
        genres: null,
        totalTime: 0,
        success: true
      };
      
      // 1. 全量采集影片数据
      winston.info('📽️ 开始全量采集影片数据...');
      try {
        results.movies = await this.syncEngine.performFullSync('movies', {
          batchSize: this.config.batchSizes.movies
        });
        winston.info(`✅ 影片全量采集完成: 成功${results.movies.successCount}, 失败${results.movies.errorCount}`);
      } catch (error) {
        winston.error('❌ 影片全量采集失败:', error);
        results.success = false;
      }
      
      // 2. 全量采集演员数据
      winston.info('👥 开始全量采集演员数据...');
      try {
        results.actors = await this.syncEngine.performFullSync('actors', {
          batchSize: this.config.batchSizes.actors
        });
        winston.info(`✅ 演员全量采集完成: 成功${results.actors.successCount}, 失败${results.actors.errorCount}`);
      } catch (error) {
        winston.error('❌ 演员全量采集失败:', error);
        results.success = false;
      }
      
      // 3. 全量采集分类数据
      winston.info('🏷️ 开始全量采集分类数据...');
      try {
        results.genres = await this.syncEngine.performFullSync('genres', {
          batchSize: this.config.batchSizes.genres
        });
        winston.info(`✅ 分类全量采集完成: 成功${results.genres.successCount}, 失败${results.genres.errorCount}`);
      } catch (error) {
        winston.error('❌ 分类全量采集失败:', error);
        results.success = false;
      }
      
      results.totalTime = Date.now() - startTime;
      
      // 生成采集报告
      const report = this.generateCollectionReport(results);
      winston.info('📊 全量采集报告:', report);
      
      winston.info(`🎉 全量数据采集完成，总耗时: ${results.totalTime}ms`);
      return results;
      
    } catch (error) {
      winston.error('❌ 全量数据采集失败:', error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }
  
  /**
   * 生成采集报告
   * @param {Object} results - 采集结果
   * @returns {Object} 采集报告
   */
  generateCollectionReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      totalTime: results.totalTime,
      success: results.success,
      summary: {
        totalProcessed: 0,
        totalSuccess: 0,
        totalErrors: 0
      },
      details: {}
    };
    
    ['movies', 'actors', 'genres'].forEach(entityType => {
      if (results[entityType]) {
        const result = results[entityType];
        report.details[entityType] = {
          processed: result.totalProcessed || 0,
          success: result.successCount || 0,
          errors: result.errorCount || 0,
          time: result.totalTime || 0,
          successRate: result.totalProcessed > 0 ? 
            ((result.successCount || 0) / result.totalProcessed * 100).toFixed(2) + '%' : '0%'
        };
        
        report.summary.totalProcessed += result.totalProcessed || 0;
        report.summary.totalSuccess += result.successCount || 0;
        report.summary.totalErrors += result.errorCount || 0;
      }
    });
    
    report.summary.overallSuccessRate = report.summary.totalProcessed > 0 ?
      (report.summary.totalSuccess / report.summary.totalProcessed * 100).toFixed(2) + '%' : '0%';
    
    return report;
  }
  
  /**
   * 获取采集状态
   */
  async getCollectionStatus() {
    const syncStats = await this.syncEngine.getSyncStatistics();
    
    return {
      isRunning: this.isRunning,
      isScheduled: !!this.cronJob,
      collectionInterval: this.config.collectionInterval,
      config: this.config,
      syncStatistics: syncStats,
      apiServiceType: this.apiService.getConfig().currentServiceType
    };
  }
  
  /**
   * 手动触发采集
   * @param {string} type - 采集类型 ('incremental' | 'full')
   */
  async triggerCollection(type = 'incremental') {
    winston.info(`🔧 手动触发${type === 'full' ? '全量' : '增量'}采集`);
    
    if (type === 'full') {
      return await this.performFullCollection();
    } else {
      return await this.performIncrementalCollection();
    }
  }
  
  /**
   * 优雅关闭服务
   */
  async shutdown() {
    winston.info('🛑 正在关闭数据采集服务...');
    
    // 停止定时任务
    this.stopScheduledCollection();
    
    // 等待当前采集任务完成
    if (this.isRunning) {
      winston.info('⏳ 等待当前采集任务完成...');
      while (this.isRunning) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    winston.info('✅ 数据采集服务已关闭');
  }
}

module.exports = DataCollectionService;
