const winston = require('winston');

/**
 * 数据验证服务类
 * 实现数据质量验证机制，确保数据的完整性和一致性
 */
class ValidationService {
  constructor() {
    // 验证规则配置
    this.validationRules = {
      movies: {
        required: ['movie_id', 'title'],
        maxLengths: {
          movie_id: 50,
          title: 255,
          duration: 50,
          description: 5000
        },
        patterns: {
          movie_id: /^[a-zA-Z0-9\-_]+$/,
          release_date: /^\d{4}-\d{2}-\d{2}$/
        }
      },
      stars: {
        required: ['star_id', 'name'],
        maxLengths: {
          star_id: 50,
          name: 100,
          birthplace: 100,
          bust: 50,
          waist: 50,
          hip: 50,
          cup_size: 10,
          measurements: 100
        },
        ranges: {
          age: { min: 18, max: 80 },
          height: { min: 100, max: 250 }
        }
      },
      genres: {
        required: ['name'],
        maxLengths: {
          name: 100
        }
      }
    };
    
    winston.info('ValidationService 初始化完成');
  }
  
  /**
   * 验证影片数据
   * @param {Object} movieData - 影片数据
   * @returns {Object} 验证结果
   */
  validateMovieData(movieData) {
    const errors = [];
    const warnings = [];
    
    try {
      // 必填字段验证
      this.validateRequiredFields(movieData, this.validationRules.movies.required, errors);
      
      // 字段长度验证
      this.validateFieldLengths(movieData, this.validationRules.movies.maxLengths, errors);
      
      // 格式验证
      this.validatePatterns(movieData, this.validationRules.movies.patterns, errors);
      
      // 业务逻辑验证
      this.validateMovieBusinessRules(movieData, errors, warnings);
      
      const isValid = errors.length === 0;
      
      if (!isValid) {
        winston.warn(`影片数据验证失败: ${movieData.movie_id}`, { errors, warnings });
      }
      
      return {
        isValid,
        errors,
        warnings,
        data: movieData
      };
      
    } catch (error) {
      winston.error('影片数据验证异常:', error);
      return {
        isValid: false,
        errors: [`验证过程异常: ${error.message}`],
        warnings: [],
        data: movieData
      };
    }
  }
  
  /**
   * 验证演员数据
   * @param {Object} starData - 演员数据
   * @returns {Object} 验证结果
   */
  validateStarData(starData) {
    const errors = [];
    const warnings = [];
    
    try {
      // 必填字段验证
      this.validateRequiredFields(starData, this.validationRules.stars.required, errors);
      
      // 字段长度验证
      this.validateFieldLengths(starData, this.validationRules.stars.maxLengths, errors);
      
      // 数值范围验证
      this.validateRanges(starData, this.validationRules.stars.ranges, errors);
      
      // 业务逻辑验证
      this.validateStarBusinessRules(starData, errors, warnings);
      
      const isValid = errors.length === 0;
      
      if (!isValid) {
        winston.warn(`演员数据验证失败: ${starData.star_id}`, { errors, warnings });
      }
      
      return {
        isValid,
        errors,
        warnings,
        data: starData
      };
      
    } catch (error) {
      winston.error('演员数据验证异常:', error);
      return {
        isValid: false,
        errors: [`验证过程异常: ${error.message}`],
        warnings: [],
        data: starData
      };
    }
  }
  
  /**
   * 验证分类数据
   * @param {Object} genreData - 分类数据
   * @returns {Object} 验证结果
   */
  validateGenreData(genreData) {
    const errors = [];
    const warnings = [];
    
    try {
      // 必填字段验证
      this.validateRequiredFields(genreData, this.validationRules.genres.required, errors);
      
      // 字段长度验证
      this.validateFieldLengths(genreData, this.validationRules.genres.maxLengths, errors);
      
      // 业务逻辑验证
      this.validateGenreBusinessRules(genreData, errors, warnings);
      
      const isValid = errors.length === 0;
      
      return {
        isValid,
        errors,
        warnings,
        data: genreData
      };
      
    } catch (error) {
      winston.error('分类数据验证异常:', error);
      return {
        isValid: false,
        errors: [`验证过程异常: ${error.message}`],
        warnings: [],
        data: genreData
      };
    }
  }
  
  /**
   * 验证必填字段
   * @param {Object} data - 数据对象
   * @param {Array} requiredFields - 必填字段列表
   * @param {Array} errors - 错误数组
   */
  validateRequiredFields(data, requiredFields, errors) {
    for (const field of requiredFields) {
      if (data[field] === null || data[field] === undefined || data[field] === '') {
        errors.push(`必填字段缺失: ${field}`);
      }
    }
  }
  
  /**
   * 验证字段长度
   * @param {Object} data - 数据对象
   * @param {Object} maxLengths - 最大长度配置
   * @param {Array} errors - 错误数组
   */
  validateFieldLengths(data, maxLengths, errors) {
    for (const [field, maxLength] of Object.entries(maxLengths)) {
      if (data[field] && typeof data[field] === 'string' && data[field].length > maxLength) {
        errors.push(`字段 ${field} 长度超限: ${data[field].length} > ${maxLength}`);
      }
    }
  }
  
  /**
   * 验证格式模式
   * @param {Object} data - 数据对象
   * @param {Object} patterns - 格式模式配置
   * @param {Array} errors - 错误数组
   */
  validatePatterns(data, patterns, errors) {
    for (const [field, pattern] of Object.entries(patterns)) {
      if (data[field] && !pattern.test(data[field])) {
        errors.push(`字段 ${field} 格式不正确: ${data[field]}`);
      }
    }
  }
  
  /**
   * 验证数值范围
   * @param {Object} data - 数据对象
   * @param {Object} ranges - 范围配置
   * @param {Array} errors - 错误数组
   */
  validateRanges(data, ranges, errors) {
    for (const [field, range] of Object.entries(ranges)) {
      if (data[field] !== null && data[field] !== undefined) {
        const value = Number(data[field]);
        if (!isNaN(value)) {
          if (range.min !== undefined && value < range.min) {
            errors.push(`字段 ${field} 值过小: ${value} < ${range.min}`);
          }
          if (range.max !== undefined && value > range.max) {
            errors.push(`字段 ${field} 值过大: ${value} > ${range.max}`);
          }
        }
      }
    }
  }
  
  /**
   * 验证影片业务规则
   * @param {Object} movieData - 影片数据
   * @param {Array} errors - 错误数组
   * @param {Array} warnings - 警告数组
   */
  validateMovieBusinessRules(movieData, errors, warnings) {
    // 发布日期不能是未来日期
    if (movieData.release_date) {
      const releaseDate = new Date(movieData.release_date);
      const today = new Date();
      if (releaseDate > today) {
        warnings.push(`发布日期是未来日期: ${movieData.release_date}`);
      }
    }
    
    // 时长格式验证
    if (movieData.duration && !movieData.duration.match(/^\d+分钟?$/)) {
      warnings.push(`时长格式可能不标准: ${movieData.duration}`);
    }
    
    // 描述长度建议
    if (movieData.description && movieData.description.length < 10) {
      warnings.push('描述内容过短，建议补充更多信息');
    }
  }
  
  /**
   * 验证演员业务规则
   * @param {Object} starData - 演员数据
   * @param {Array} errors - 错误数组
   * @param {Array} warnings - 警告数组
   */
  validateStarBusinessRules(starData, errors, warnings) {
    // 生日和年龄一致性检查
    if (starData.birthday && starData.age) {
      const birthYear = new Date(starData.birthday).getFullYear();
      const currentYear = new Date().getFullYear();
      const calculatedAge = currentYear - birthYear;
      
      if (Math.abs(calculatedAge - starData.age) > 1) {
        warnings.push(`年龄与生日不一致: 生日${starData.birthday}, 年龄${starData.age}`);
      }
    }
    
    // 出道日期不能早于生日
    if (starData.birthday && starData.debut_date) {
      const birthday = new Date(starData.birthday);
      const debutDate = new Date(starData.debut_date);
      
      if (debutDate < birthday) {
        errors.push('出道日期不能早于生日');
      }
    }
    
    // 身体数据合理性检查
    if (starData.bust && starData.waist && starData.hip) {
      const bustNum = parseInt(starData.bust);
      const waistNum = parseInt(starData.waist);
      const hipNum = parseInt(starData.hip);
      
      if (!isNaN(bustNum) && !isNaN(waistNum) && !isNaN(hipNum)) {
        if (waistNum >= bustNum || waistNum >= hipNum) {
          warnings.push('身体数据可能不合理: 腰围应小于胸围和臀围');
        }
      }
    }
  }
  
  /**
   * 验证分类业务规则
   * @param {Object} genreData - 分类数据
   * @param {Array} errors - 错误数组
   * @param {Array} warnings - 警告数组
   */
  validateGenreBusinessRules(genreData, errors, warnings) {
    // 分类名称不能包含特殊字符
    if (genreData.name && genreData.name.match(/[<>\"'&]/)) {
      errors.push('分类名称不能包含特殊字符');
    }

    // 分类名称长度建议
    if (genreData.name && genreData.name.length < 2) {
      warnings.push('分类名称过短');
    }
  }

  /**
   * 批量验证影片数据
   * @param {Array} moviesData - 影片数据数组
   * @returns {Object} 批量验证结果
   */
  validateMoviesData(moviesData) {
    if (!Array.isArray(moviesData)) {
      throw new Error('输入必须是数组');
    }

    const results = {
      valid: [],
      invalid: [],
      duplicates: [],
      total: moviesData.length,
      validCount: 0,
      invalidCount: 0,
      duplicateCount: 0,
      summary: {
        errors: {},
        warnings: {}
      }
    };

    const seenIds = new Set();

    for (let i = 0; i < moviesData.length; i++) {
      const movieData = moviesData[i];

      // 重复检查
      if (movieData.movie_id && seenIds.has(movieData.movie_id)) {
        results.duplicates.push({
          index: i,
          movie_id: movieData.movie_id,
          data: movieData
        });
        results.duplicateCount++;
        continue;
      }

      if (movieData.movie_id) {
        seenIds.add(movieData.movie_id);
      }

      // 数据验证
      const validation = this.validateMovieData(movieData);

      if (validation.isValid) {
        results.valid.push({
          index: i,
          data: validation.data,
          warnings: validation.warnings
        });
        results.validCount++;
      } else {
        results.invalid.push({
          index: i,
          data: validation.data,
          errors: validation.errors,
          warnings: validation.warnings
        });
        results.invalidCount++;
      }

      // 统计错误和警告
      validation.errors.forEach(error => {
        results.summary.errors[error] = (results.summary.errors[error] || 0) + 1;
      });

      validation.warnings.forEach(warning => {
        results.summary.warnings[warning] = (results.summary.warnings[warning] || 0) + 1;
      });
    }

    winston.info(`批量影片验证完成: 总数${results.total}, 有效${results.validCount}, 无效${results.invalidCount}, 重复${results.duplicateCount}`);

    return results;
  }

  /**
   * 批量验证演员数据
   * @param {Array} starsData - 演员数据数组
   * @returns {Object} 批量验证结果
   */
  validateStarsData(starsData) {
    if (!Array.isArray(starsData)) {
      throw new Error('输入必须是数组');
    }

    const results = {
      valid: [],
      invalid: [],
      duplicates: [],
      total: starsData.length,
      validCount: 0,
      invalidCount: 0,
      duplicateCount: 0,
      summary: {
        errors: {},
        warnings: {}
      }
    };

    const seenIds = new Set();

    for (let i = 0; i < starsData.length; i++) {
      const starData = starsData[i];

      // 重复检查
      if (starData.star_id && seenIds.has(starData.star_id)) {
        results.duplicates.push({
          index: i,
          star_id: starData.star_id,
          data: starData
        });
        results.duplicateCount++;
        continue;
      }

      if (starData.star_id) {
        seenIds.add(starData.star_id);
      }

      // 数据验证
      const validation = this.validateStarData(starData);

      if (validation.isValid) {
        results.valid.push({
          index: i,
          data: validation.data,
          warnings: validation.warnings
        });
        results.validCount++;
      } else {
        results.invalid.push({
          index: i,
          data: validation.data,
          errors: validation.errors,
          warnings: validation.warnings
        });
        results.invalidCount++;
      }

      // 统计错误和警告
      validation.errors.forEach(error => {
        results.summary.errors[error] = (results.summary.errors[error] || 0) + 1;
      });

      validation.warnings.forEach(warning => {
        results.summary.warnings[warning] = (results.summary.warnings[warning] || 0) + 1;
      });
    }

    winston.info(`批量演员验证完成: 总数${results.total}, 有效${results.validCount}, 无效${results.invalidCount}, 重复${results.duplicateCount}`);

    return results;
  }

  /**
   * 生成数据质量报告
   * @param {Object} validationResults - 验证结果
   * @param {string} dataType - 数据类型 ('movies', 'stars', 'genres')
   * @returns {Object} 数据质量报告
   */
  generateQualityReport(validationResults, dataType) {
    const report = {
      dataType,
      timestamp: new Date().toISOString(),
      overview: {
        total: validationResults.total,
        valid: validationResults.validCount,
        invalid: validationResults.invalidCount,
        duplicates: validationResults.duplicateCount || 0,
        validRate: validationResults.total > 0 ? (validationResults.validCount / validationResults.total * 100).toFixed(2) : 0,
        duplicateRate: validationResults.total > 0 ? ((validationResults.duplicateCount || 0) / validationResults.total * 100).toFixed(2) : 0
      },
      errors: {
        summary: validationResults.summary?.errors || {},
        topErrors: this.getTopIssues(validationResults.summary?.errors || {}, 5),
        totalErrorCount: Object.values(validationResults.summary?.errors || {}).reduce((sum, count) => sum + count, 0)
      },
      warnings: {
        summary: validationResults.summary?.warnings || {},
        topWarnings: this.getTopIssues(validationResults.summary?.warnings || {}, 5),
        totalWarningCount: Object.values(validationResults.summary?.warnings || {}).reduce((sum, count) => sum + count, 0)
      },
      recommendations: this.generateRecommendations(validationResults, dataType)
    };

    winston.info(`数据质量报告生成完成: ${dataType}`, {
      total: report.overview.total,
      validRate: report.overview.validRate + '%',
      duplicateRate: report.overview.duplicateRate + '%'
    });

    return report;
  }

  /**
   * 获取最常见的问题
   * @param {Object} issues - 问题统计
   * @param {number} limit - 返回数量限制
   * @returns {Array} 排序后的问题列表
   */
  getTopIssues(issues, limit = 5) {
    return Object.entries(issues)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([issue, count]) => ({ issue, count }));
  }

  /**
   * 生成改进建议
   * @param {Object} validationResults - 验证结果
   * @param {string} dataType - 数据类型
   * @returns {Array} 建议列表
   */
  generateRecommendations(validationResults, dataType) {
    const recommendations = [];

    // 基于验证率的建议
    const validRate = validationResults.validCount / validationResults.total;
    if (validRate < 0.8) {
      recommendations.push('数据质量较低，建议检查数据源和映射逻辑');
    }

    // 基于重复率的建议
    const duplicateRate = (validationResults.duplicateCount || 0) / validationResults.total;
    if (duplicateRate > 0.1) {
      recommendations.push('重复数据较多，建议优化去重逻辑');
    }

    // 基于常见错误的建议
    const topErrors = this.getTopIssues(validationResults.summary?.errors || {}, 3);
    topErrors.forEach(({ issue, count }) => {
      if (issue.includes('必填字段缺失')) {
        recommendations.push('存在必填字段缺失，建议检查数据完整性');
      }
      if (issue.includes('长度超限')) {
        recommendations.push('存在字段长度超限，建议调整数据清洗规则');
      }
      if (issue.includes('格式不正确')) {
        recommendations.push('存在格式错误，建议优化数据格式化逻辑');
      }
    });

    // 数据类型特定建议
    if (dataType === 'movies') {
      if (validationResults.summary?.warnings?.['描述内容过短']) {
        recommendations.push('影片描述普遍过短，建议丰富描述内容');
      }
    }

    if (dataType === 'stars') {
      if (validationResults.summary?.warnings?.['年龄与生日不一致']) {
        recommendations.push('演员年龄与生日不一致，建议核实个人信息');
      }
    }

    return recommendations;
  }
}

module.exports = ValidationService;
