const winston = require('winston');
const ApiService = require('./ApiService');
const MockApiService = require('./MockApiService');

/**
 * 自适应API服务类
 * 智能检测外网API可用性，自动切换到模拟服务
 */
class AdaptiveApiService {
  constructor() {
    this.realApiService = new ApiService();
    this.mockApiService = new MockApiService();
    this.currentService = null;
    this.lastHealthCheck = null;
    this.healthCheckInterval = 5 * 60 * 1000; // 5分钟检查一次
    this.useRealApi = process.env.USE_REAL_API !== 'false'; // 默认尝试使用真实API
    
    winston.info('AdaptiveApiService 初始化完成');
  }
  
  /**
   * 初始化服务，检测API可用性
   */
  async initialize() {
    winston.info('正在初始化自适应API服务...');
    
    if (this.useRealApi) {
      const isRealApiAvailable = await this.checkRealApiAvailability();
      if (isRealApiAvailable) {
        this.currentService = this.realApiService;
        winston.info('✅ 使用真实外网API服务');
      } else {
        this.currentService = this.mockApiService;
        winston.warn('⚠️ 外网API不可用，切换到模拟API服务');
      }
    } else {
      this.currentService = this.mockApiService;
      winston.info('🔧 配置为使用模拟API服务');
    }
    
    return this.currentService;
  }
  
  /**
   * 检查真实API的可用性
   * @returns {Promise<boolean>} API是否可用
   */
  async checkRealApiAvailability() {
    try {
      winston.info('检查外网API可用性...');
      const healthStatus = await this.realApiService.healthCheck();
      
      if (healthStatus.status === 'healthy') {
        // 进一步检查是否有可用的数据端点
        const discovery = await this.realApiService.discoverEndpoints([
          '/api/v1/movies',
          '/movies',
          '/api/data',
          '/data'
        ]);
        
        const hasDataEndpoints = discovery.available.length > 0;
        winston.info(`外网API健康检查: ${healthStatus.status}, 可用端点: ${discovery.available.length}个`);
        
        return hasDataEndpoints;
      }
      
      return false;
    } catch (error) {
      winston.error('外网API健康检查失败:', error.message);
      return false;
    }
  }
  
  /**
   * 获取当前使用的服务
   * @returns {Object} 当前API服务实例
   */
  async getCurrentService() {
    if (!this.currentService) {
      await this.initialize();
    }
    
    // 定期检查API状态
    const now = Date.now();
    if (this.lastHealthCheck && (now - this.lastHealthCheck) > this.healthCheckInterval) {
      await this.periodicHealthCheck();
    }
    
    return this.currentService;
  }
  
  /**
   * 定期健康检查
   */
  async periodicHealthCheck() {
    this.lastHealthCheck = Date.now();
    
    if (this.currentService === this.mockApiService && this.useRealApi) {
      // 当前使用模拟服务，检查真实API是否恢复
      const isRealApiAvailable = await this.checkRealApiAvailability();
      if (isRealApiAvailable) {
        winston.info('🔄 外网API已恢复，切换到真实API服务');
        this.currentService = this.realApiService;
      }
    } else if (this.currentService === this.realApiService) {
      // 当前使用真实API，检查是否仍然可用
      const isRealApiAvailable = await this.checkRealApiAvailability();
      if (!isRealApiAvailable) {
        winston.warn('⚠️ 外网API不可用，切换到模拟API服务');
        this.currentService = this.mockApiService;
      }
    }
  }
  
  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    const service = await this.getCurrentService();
    const result = await service.healthCheck();
    
    // 添加服务类型信息
    result.serviceType = service === this.realApiService ? 'real' : 'mock';
    result.adaptiveService = true;
    
    return result;
  }
  
  /**
   * 获取最新影片列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 影片列表
   */
  async getLatestMovies(params = {}) {
    const service = await this.getCurrentService();
    try {
      return await service.getLatestMovies(params);
    } catch (error) {
      return await this.handleServiceError(error, 'getLatestMovies', params);
    }
  }
  
  /**
   * 获取热门影片列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 影片列表
   */
  async getPopularMovies(params = {}) {
    const service = await this.getCurrentService();
    try {
      return await service.getPopularMovies(params);
    } catch (error) {
      return await this.handleServiceError(error, 'getPopularMovies', params);
    }
  }
  
  /**
   * 获取影片详情
   * @param {string} movieId - 影片ID
   * @returns {Promise<Object>} 影片详情
   */
  async getMovieDetail(movieId) {
    const service = await this.getCurrentService();
    try {
      return await service.getMovieDetail(movieId);
    } catch (error) {
      return await this.handleServiceError(error, 'getMovieDetail', movieId);
    }
  }
  
  /**
   * 获取演员列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 演员列表
   */
  async getActors(params = {}) {
    const service = await this.getCurrentService();
    try {
      return await service.getActors(params);
    } catch (error) {
      return await this.handleServiceError(error, 'getActors', params);
    }
  }
  
  /**
   * 获取分类列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 分类列表
   */
  async getGenres(params = {}) {
    const service = await this.getCurrentService();
    try {
      return await service.getGenres(params);
    } catch (error) {
      return await this.handleServiceError(error, 'getGenres', params);
    }
  }
  
  /**
   * 获取服务器统计
   * @returns {Promise<Object>} 统计信息
   */
  async getServerStats() {
    const service = await this.getCurrentService();
    try {
      return await service.getServerStats();
    } catch (error) {
      return await this.handleServiceError(error, 'getServerStats');
    }
  }
  
  /**
   * 处理服务错误，尝试切换服务
   * @param {Error} error - 错误对象
   * @param {string} methodName - 方法名
   * @param {*} params - 参数
   * @returns {Promise<Object>} 备用服务的结果
   */
  async handleServiceError(error, methodName, params) {
    winston.error(`API服务错误 (${methodName}):`, error.message);
    
    // 如果当前使用真实API且出错，尝试切换到模拟API
    if (this.currentService === this.realApiService) {
      winston.warn('🔄 真实API出错，尝试切换到模拟API服务');
      this.currentService = this.mockApiService;
      
      try {
        return await this.mockApiService[methodName](params);
      } catch (mockError) {
        winston.error('模拟API也出错:', mockError.message);
        throw error; // 抛出原始错误
      }
    }
    
    throw error;
  }
  
  /**
   * 强制切换到模拟服务
   */
  switchToMockService() {
    winston.info('🔧 手动切换到模拟API服务');
    this.currentService = this.mockApiService;
  }
  
  /**
   * 强制切换到真实服务
   */
  async switchToRealService() {
    winston.info('🔧 手动切换到真实API服务');
    const isAvailable = await this.checkRealApiAvailability();
    if (isAvailable) {
      this.currentService = this.realApiService;
      winston.info('✅ 成功切换到真实API服务');
    } else {
      winston.warn('⚠️ 真实API不可用，保持使用模拟服务');
      throw new Error('真实API服务不可用');
    }
  }
  
  /**
   * 获取配置信息
   * @returns {Object} 配置信息
   */
  getConfig() {
    const baseConfig = {
      adaptiveService: true,
      useRealApi: this.useRealApi,
      healthCheckInterval: this.healthCheckInterval,
      currentServiceType: this.currentService === this.realApiService ? 'real' : 'mock'
    };
    
    if (this.currentService) {
      return {
        ...baseConfig,
        ...this.currentService.getConfig()
      };
    }
    
    return baseConfig;
  }
}

module.exports = AdaptiveApiService;
