const winston = require('winston');
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || "postgresql://postgres:password@localhost:5432/javflix"
});

/**
 * 增量同步引擎类
 * 基于时间戳的增量同步机制，只同步新增和更新的数据
 * 集成AdaptiveApiService、DataMapper和ValidationService
 */
class SyncEngine {
  constructor(apiService, dataMapper, validationService) {
    this.apiService = apiService;
    this.dataMapper = dataMapper;
    this.validationService = validationService;
    
    // 同步配置
    this.syncConfig = {
      batchSize: {
        movies: 100,
        actors: 50,
        genres: 20
      },
      maxRetries: 3,
      retryDelay: 5000, // 5秒
      timeoutMs: 300000 // 5分钟超时
    };
    
    winston.info('SyncEngine 初始化完成');
  }
  
  /**
   * 执行增量同步
   * @param {string} entityType - 实体类型 ('movies', 'actors', 'genres')
   * @param {Object} options - 同步选项
   * @returns {Promise<Object>} 同步结果
   */
  async performIncrementalSync(entityType, options = {}) {
    const startTime = Date.now();
    winston.info(`开始增量同步: ${entityType}`);
    
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // 1. 获取同步状态
      const syncStatus = await this.getSyncStatus(client, entityType);
      
      // 2. 更新同步状态为运行中
      await this.updateSyncStatus(client, entityType, {
        status: 'running',
        last_error_message: null
      });
      
      // 3. 确定同步时间范围
      const syncTimeRange = this.determineSyncTimeRange(syncStatus, options);
      winston.info(`同步时间范围: ${syncTimeRange.since} - ${syncTimeRange.until}`);
      
      // 4. 执行分批同步
      const syncResult = await this.performBatchSync(client, entityType, syncTimeRange, options);
      
      // 5. 更新同步状态
      await this.updateSyncStatus(client, entityType, {
        status: syncResult.success ? 'completed' : 'failed',
        last_sync_time: new Date(),
        total_synced: (syncStatus.total_synced || 0) + syncResult.successCount,
        errors_count: (syncStatus.errors_count || 0) + syncResult.errorCount,
        last_error_message: syncResult.lastError
      });
      
      await client.query('COMMIT');
      
      const totalTime = Date.now() - startTime;
      winston.info(`增量同步完成: ${entityType}, 耗时: ${totalTime}ms`);
      
      return {
        success: syncResult.success,
        entityType,
        syncTimeRange,
        totalTime,
        ...syncResult
      };
      
    } catch (error) {
      await client.query('ROLLBACK');
      winston.error(`增量同步失败: ${entityType}`, error);
      
      // 记录错误状态
      try {
        await this.updateSyncStatus(client, entityType, {
          status: 'failed',
          last_error_message: error.message
        });
      } catch (updateError) {
        winston.error('更新同步状态失败:', updateError);
      }
      
      throw error;
    } finally {
      client.release();
    }
  }
  
  /**
   * 执行全量同步
   * @param {string} entityType - 实体类型
   * @param {Object} options - 同步选项
   * @returns {Promise<Object>} 同步结果
   */
  async performFullSync(entityType, options = {}) {
    winston.info(`开始全量同步: ${entityType}`);
    
    // 全量同步不使用时间范围限制
    const fullSyncOptions = {
      ...options,
      isFullSync: true,
      since: null
    };
    
    return this.performIncrementalSync(entityType, fullSyncOptions);
  }
  
  /**
   * 获取同步状态
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @returns {Promise<Object>} 同步状态
   */
  async getSyncStatus(client, entityType) {
    const result = await client.query(
      'SELECT * FROM sync_status WHERE entity_type = $1',
      [entityType]
    );
    
    if (result.rows.length === 0) {
      // 创建默认同步状态
      const defaultConfig = this.getDefaultSyncConfig(entityType);
      await client.query(
        `INSERT INTO sync_status (entity_type, status, sync_config) 
         VALUES ($1, $2, $3) RETURNING *`,
        [entityType, 'idle', JSON.stringify(defaultConfig)]
      );
      
      return {
        entity_type: entityType,
        status: 'idle',
        total_synced: 0,
        errors_count: 0,
        sync_config: defaultConfig
      };
    }
    
    return result.rows[0];
  }
  
  /**
   * 更新同步状态
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @param {Object} updates - 更新数据
   */
  async updateSyncStatus(client, entityType, updates) {
    const setClause = Object.keys(updates).map((key, index) => `${key} = $${index + 2}`).join(', ');
    const values = [entityType, ...Object.values(updates)];
    
    await client.query(
      `UPDATE sync_status SET ${setClause} WHERE entity_type = $1`,
      values
    );
  }
  
  /**
   * 确定同步时间范围
   * @param {Object} syncStatus - 同步状态
   * @param {Object} options - 选项
   * @returns {Object} 时间范围
   */
  determineSyncTimeRange(syncStatus, options) {
    const now = new Date();
    
    if (options.isFullSync) {
      return {
        since: null,
        until: now,
        isFullSync: true
      };
    }
    
    // 增量同步：从上次同步时间开始
    const since = options.since || syncStatus.last_sync_time || new Date(Date.now() - 24 * 60 * 60 * 1000); // 默认24小时前
    
    return {
      since: since instanceof Date ? since : new Date(since),
      until: options.until || now,
      isFullSync: false
    };
  }
  
  /**
   * 执行分批同步
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @param {Object} timeRange - 时间范围
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 同步结果
   */
  async performBatchSync(client, entityType, timeRange, options) {
    const batchSize = options.batchSize || this.syncConfig.batchSize[entityType] || 50;
    let page = 1;
    let hasMore = true;
    let totalProcessed = 0;
    let successCount = 0;
    let errorCount = 0;
    let lastError = null;
    
    while (hasMore) {
      try {
        winston.info(`处理第 ${page} 批数据: ${entityType}`);
        
        // 1. 从API获取数据
        const apiData = await this.fetchApiData(entityType, {
          page,
          limit: batchSize,
          since: timeRange.since,
          until: timeRange.until
        });
        
        if (!apiData || !apiData.data || apiData.data.length === 0) {
          winston.info(`第 ${page} 批数据为空，同步完成`);
          hasMore = false;
          break;
        }
        
        // 2. 处理批次数据
        const batchResult = await this.processBatch(client, entityType, apiData.data);
        
        totalProcessed += apiData.data.length;
        successCount += batchResult.successCount;
        errorCount += batchResult.errorCount;
        
        if (batchResult.lastError) {
          lastError = batchResult.lastError;
        }
        
        // 3. 记录性能指标
        await this.recordPerformanceMetrics(client, entityType, {
          batch_size: apiData.data.length,
          processing_time: batchResult.processingTime,
          success_rate: batchResult.successCount / apiData.data.length
        });
        
        // 4. 检查是否还有更多数据
        hasMore = apiData.pagination ? apiData.pagination.hasNext : false;
        page++;
        
        // 5. 批次间延迟，避免API限流
        if (hasMore) {
          await this.delay(1000); // 1秒延迟
        }
        
      } catch (error) {
        winston.error(`处理第 ${page} 批数据失败:`, error);
        errorCount++;
        lastError = error.message;
        
        // 记录错误日志
        await this.logSyncOperation(client, entityType, null, null, 'error', {
          batch: page,
          error: error.message
        }, error.message);
        
        // 如果是网络错误，可以重试
        if (this.isRetryableError(error) && options.retryCount < this.syncConfig.maxRetries) {
          winston.warn(`第 ${page} 批数据处理失败，${this.syncConfig.retryDelay}ms后重试...`);
          await this.delay(this.syncConfig.retryDelay);
          options.retryCount = (options.retryCount || 0) + 1;
          continue; // 重试当前批次
        }
        
        // 非重试错误或重试次数超限，继续下一批
        page++;
      }
    }
    
    return {
      success: errorCount === 0 || successCount > 0,
      totalProcessed,
      successCount,
      errorCount,
      lastError
    };
  }

  /**
   * 从API获取数据
   * @param {string} entityType - 实体类型
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} API数据
   */
  async fetchApiData(entityType, params) {
    const startTime = Date.now();

    try {
      let apiData;

      switch (entityType) {
        case 'movies':
          apiData = await this.apiService.getLatestMovies(params);
          break;
        case 'actors':
          apiData = await this.apiService.getActors(params);
          break;
        case 'genres':
          apiData = await this.apiService.getGenres(params);
          break;
        default:
          throw new Error(`不支持的实体类型: ${entityType}`);
      }

      const responseTime = Date.now() - startTime;
      winston.debug(`API响应时间: ${responseTime}ms, 数据量: ${apiData?.data?.length || 0}`);

      return apiData;

    } catch (error) {
      winston.error(`API数据获取失败: ${entityType}`, error);
      throw error;
    }
  }

  /**
   * 处理批次数据
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @param {Array} batchData - 批次数据
   * @returns {Promise<Object>} 处理结果
   */
  async processBatch(client, entityType, batchData) {
    const startTime = Date.now();
    let successCount = 0;
    let errorCount = 0;
    let lastError = null;

    try {
      // 1. 数据映射
      const mappingResult = this.mapBatchData(entityType, batchData);
      winston.debug(`数据映射完成: 成功${mappingResult.successCount}, 失败${mappingResult.errorCount}`);

      if (mappingResult.success.length === 0) {
        winston.warn('批次数据映射后无有效数据');
        return { successCount: 0, errorCount: batchData.length, lastError: '数据映射失败' };
      }

      // 2. 数据验证
      const validationResult = this.validateBatchData(entityType, mappingResult.success);
      winston.debug(`数据验证完成: 有效${validationResult.validCount}, 无效${validationResult.invalidCount}`);

      // 3. 数据去重和保存
      for (const validItem of validationResult.valid) {
        try {
          const saveResult = await this.saveEntityData(client, entityType, validItem.data);

          // 记录操作日志
          await this.logSyncOperation(
            client,
            entityType,
            saveResult.entityId,
            saveResult.localId,
            saveResult.action,
            {
              title: saveResult.title,
              isNew: saveResult.action === 'create'
            },
            null,
            Date.now() - startTime
          );

          successCount++;

        } catch (saveError) {
          winston.error(`保存数据失败: ${validItem.data.movie_id || validItem.data.star_id || validItem.data.name}`, saveError);

          // 记录错误日志
          await this.logSyncOperation(
            client,
            entityType,
            validItem.data.movie_id || validItem.data.star_id || validItem.data.name,
            null,
            'error',
            validItem.data,
            saveError.message
          );

          errorCount++;
          lastError = saveError.message;
        }
      }

      // 4. 处理验证失败的数据
      for (const invalidItem of validationResult.invalid) {
        await this.logSyncOperation(
          client,
          entityType,
          invalidItem.data.movie_id || invalidItem.data.star_id || invalidItem.data.name,
          null,
          'error',
          invalidItem.data,
          `验证失败: ${invalidItem.errors.join(', ')}`
        );
        errorCount++;
      }

      const processingTime = Date.now() - startTime;

      return {
        successCount,
        errorCount,
        lastError,
        processingTime
      };

    } catch (error) {
      winston.error('批次数据处理失败:', error);
      return {
        successCount,
        errorCount: batchData.length,
        lastError: error.message,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * 映射批次数据
   * @param {string} entityType - 实体类型
   * @param {Array} batchData - 批次数据
   * @returns {Object} 映射结果
   */
  mapBatchData(entityType, batchData) {
    try {
      switch (entityType) {
        case 'movies':
          return this.dataMapper.mapMoviesData(batchData);
        case 'actors':
          return this.dataMapper.mapStarsData(batchData);
        case 'genres':
          return {
            success: batchData.map(genre => this.dataMapper.mapGenreData(genre)),
            errors: [],
            successCount: batchData.length,
            errorCount: 0
          };
        default:
          throw new Error(`不支持的实体类型: ${entityType}`);
      }
    } catch (error) {
      winston.error(`数据映射失败: ${entityType}`, error);
      return {
        success: [],
        errors: batchData.map((item, index) => ({ index, data: item, error: error.message })),
        successCount: 0,
        errorCount: batchData.length
      };
    }
  }

  /**
   * 验证批次数据
   * @param {string} entityType - 实体类型
   * @param {Array} mappedData - 映射后的数据
   * @returns {Object} 验证结果
   */
  validateBatchData(entityType, mappedData) {
    try {
      switch (entityType) {
        case 'movies':
          return this.validationService.validateMoviesData(mappedData);
        case 'actors':
          return this.validationService.validateStarsData(mappedData);
        case 'genres':
          // 简单验证分类数据
          const valid = [];
          const invalid = [];

          mappedData.forEach((genre, index) => {
            const validation = this.validationService.validateGenreData(genre);
            if (validation.isValid) {
              valid.push({ index, data: validation.data, warnings: validation.warnings });
            } else {
              invalid.push({ index, data: validation.data, errors: validation.errors });
            }
          });

          return {
            valid,
            invalid,
            validCount: valid.length,
            invalidCount: invalid.length,
            total: mappedData.length
          };
        default:
          throw new Error(`不支持的实体类型: ${entityType}`);
      }
    } catch (error) {
      winston.error(`数据验证失败: ${entityType}`, error);
      return {
        valid: [],
        invalid: mappedData.map((item, index) => ({ index, data: item, errors: [error.message] })),
        validCount: 0,
        invalidCount: mappedData.length,
        total: mappedData.length
      };
    }
  }

  /**
   * 保存实体数据（支持去重和更新）
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @param {Object} entityData - 实体数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveEntityData(client, entityType, entityData) {
    try {
      switch (entityType) {
        case 'movies':
          return await this.saveMovieData(client, entityData);
        case 'actors':
          return await this.saveActorData(client, entityData);
        case 'genres':
          return await this.saveGenreData(client, entityData);
        default:
          throw new Error(`不支持的实体类型: ${entityType}`);
      }
    } catch (error) {
      winston.error(`保存实体数据失败: ${entityType}`, error);
      throw error;
    }
  }

  /**
   * 保存影片数据
   * @param {Object} client - 数据库客户端
   * @param {Object} movieData - 影片数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveMovieData(client, movieData) {
    // 检查是否已存在
    const existingResult = await client.query(
      'SELECT id, title, updated_at FROM movies WHERE movie_id = $1',
      [movieData.movie_id]
    );

    if (existingResult.rows.length > 0) {
      // 更新现有记录
      const existingMovie = existingResult.rows[0];

      const updateResult = await client.query(
        `UPDATE movies SET
         title = $2, image_url = $3, cover_image = $4, cached_image_url = $5,
         release_date = $6, duration = $7, description = $8, updated_at = NOW()
         WHERE movie_id = $1 RETURNING id`,
        [
          movieData.movie_id, movieData.title, movieData.image_url, movieData.cover_image,
          movieData.cached_image_url, movieData.release_date, movieData.duration,
          movieData.description
        ]
      );

      return {
        action: 'update',
        entityId: movieData.movie_id,
        localId: updateResult.rows[0].id,
        title: movieData.title
      };
    } else {
      // 创建新记录
      const insertResult = await client.query(
        `INSERT INTO movies
         (movie_id, title, image_url, cover_image, cached_image_url, release_date,
          duration, description, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
         RETURNING id`,
        [
          movieData.movie_id, movieData.title, movieData.image_url, movieData.cover_image,
          movieData.cached_image_url, movieData.release_date, movieData.duration,
          movieData.description
        ]
      );

      return {
        action: 'create',
        entityId: movieData.movie_id,
        localId: insertResult.rows[0].id,
        title: movieData.title
      };
    }
  }

  /**
   * 保存演员数据
   * @param {Object} client - 数据库客户端
   * @param {Object} actorData - 演员数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveActorData(client, actorData) {
    // 检查是否已存在
    const existingResult = await client.query(
      'SELECT id, name, updated_at FROM stars WHERE star_id = $1',
      [actorData.star_id]
    );

    if (existingResult.rows.length > 0) {
      // 更新现有记录
      const updateResult = await client.query(
        `UPDATE stars SET
         name = $2, image_url = $3, cached_image_url = $4, birthday = $5,
         age = $6, height = $7, birthplace = $8, debut_date = $9, hobby = $10,
         bust = $11, waist = $12, hip = $13, cup_size = $14, measurements = $15,
         description = $16, movie_count = $17, updated_at = NOW()
         WHERE star_id = $1 RETURNING id`,
        [
          actorData.star_id, actorData.name, actorData.image_url, actorData.cached_image_url,
          actorData.birthday, actorData.age, actorData.height, actorData.birthplace,
          actorData.debut_date, actorData.hobby, actorData.bust, actorData.waist,
          actorData.hip, actorData.cup_size, actorData.measurements, actorData.description,
          actorData.movie_count
        ]
      );

      return {
        action: 'update',
        entityId: actorData.star_id,
        localId: updateResult.rows[0].id,
        title: actorData.name
      };
    } else {
      // 创建新记录
      const insertResult = await client.query(
        `INSERT INTO stars
         (star_id, name, image_url, cached_image_url, birthday, age, height, birthplace,
          debut_date, hobby, bust, waist, hip, cup_size, measurements, description,
          movie_count, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW(), NOW())
         RETURNING id`,
        [
          actorData.star_id, actorData.name, actorData.image_url, actorData.cached_image_url,
          actorData.birthday, actorData.age, actorData.height, actorData.birthplace,
          actorData.debut_date, actorData.hobby, actorData.bust, actorData.waist,
          actorData.hip, actorData.cup_size, actorData.measurements, actorData.description,
          actorData.movie_count
        ]
      );

      return {
        action: 'create',
        entityId: actorData.star_id,
        localId: insertResult.rows[0].id,
        title: actorData.name
      };
    }
  }

  /**
   * 保存分类数据
   * @param {Object} client - 数据库客户端
   * @param {Object} genreData - 分类数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveGenreData(client, genreData) {
    // 检查是否已存在
    const existingResult = await client.query(
      'SELECT id, name, updated_at FROM genres WHERE name = $1',
      [genreData.name]
    );

    if (existingResult.rows.length > 0) {
      // 分类通常不需要更新，直接跳过
      return {
        action: 'skip',
        entityId: genreData.name,
        localId: existingResult.rows[0].id,
        title: genreData.name
      };
    } else {
      // 创建新记录
      const insertResult = await client.query(
        `INSERT INTO genres (name, created_at, updated_at)
         VALUES ($1, NOW(), NOW()) RETURNING id`,
        [genreData.name]
      );

      return {
        action: 'create',
        entityId: genreData.name,
        localId: insertResult.rows[0].id,
        title: genreData.name
      };
    }
  }

  /**
   * 记录同步操作日志
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @param {string} entityId - 实体ID
   * @param {number} localId - 本地ID
   * @param {string} action - 操作类型
   * @param {Object} details - 详细信息
   * @param {string} errorMessage - 错误信息
   * @param {number} processingTime - 处理时间
   */
  async logSyncOperation(client, entityType, entityId, localId, action, details = {}, errorMessage = null, processingTime = null) {
    try {
      await client.query(
        `INSERT INTO sync_logs
         (entity_type, entity_id, local_id, action, details, error_message, processing_time)
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          entityType,
          entityId,
          localId,
          action,
          JSON.stringify(details),
          errorMessage,
          processingTime
        ]
      );
    } catch (error) {
      winston.error('记录同步日志失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 记录性能指标
   * @param {Object} client - 数据库客户端
   * @param {string} entityType - 实体类型
   * @param {Object} metrics - 性能指标
   */
  async recordPerformanceMetrics(client, entityType, metrics) {
    try {
      for (const [metricName, metricValue] of Object.entries(metrics)) {
        await client.query(
          `INSERT INTO sync_performance_metrics (entity_type, metric_name, metric_value)
           VALUES ($1, $2, $3)`,
          [entityType, metricName, metricValue]
        );
      }
    } catch (error) {
      winston.error('记录性能指标失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 获取默认同步配置
   * @param {string} entityType - 实体类型
   * @returns {Object} 默认配置
   */
  getDefaultSyncConfig(entityType) {
    const baseConfig = {
      batch_size: this.syncConfig.batchSize[entityType] || 50,
      max_retries: this.syncConfig.maxRetries,
      retry_delay: this.syncConfig.retryDelay,
      timeout_ms: this.syncConfig.timeoutMs
    };

    switch (entityType) {
      case 'movies':
        return {
          ...baseConfig,
          api_endpoint: '/api/v1/movies',
          sync_fields: ['title', 'release_date', 'duration', 'description', 'image_url']
        };
      case 'actors':
        return {
          ...baseConfig,
          api_endpoint: '/api/v1/actors',
          sync_fields: ['name', 'birthday', 'age', 'height', 'birthplace']
        };
      case 'genres':
        return {
          ...baseConfig,
          api_endpoint: '/api/v1/genres',
          sync_fields: ['name']
        };
      default:
        return baseConfig;
    }
  }

  /**
   * 判断是否为可重试的错误
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否可重试
   */
  isRetryableError(error) {
    // 网络错误、超时错误、5xx服务器错误可以重试
    if (!error.response) return true; // 网络错误

    const status = error.response.status;
    return status >= 500 || status === 429; // 服务器错误或限流
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取同步统计信息
   * @param {string} entityType - 实体类型（可选）
   * @returns {Promise<Object>} 统计信息
   */
  async getSyncStatistics(entityType = null) {
    const client = await pool.connect();

    try {
      let query = `
        SELECT
          ss.entity_type,
          ss.status,
          ss.last_sync_time,
          ss.total_synced,
          ss.errors_count,
          COALESCE(recent_logs.recent_success, 0) as recent_success_count,
          COALESCE(recent_logs.recent_errors, 0) as recent_error_count,
          CASE
            WHEN ss.total_synced > 0 THEN
              ROUND((ss.total_synced - ss.errors_count) * 100.0 / ss.total_synced, 2)
            ELSE 0
          END as success_rate
        FROM sync_status ss
        LEFT JOIN (
          SELECT
            entity_type,
            COUNT(CASE WHEN action IN ('create', 'update') THEN 1 END) as recent_success,
            COUNT(CASE WHEN action = 'error' THEN 1 END) as recent_errors
          FROM sync_logs
          WHERE created_at >= NOW() - INTERVAL '24 hours'
          GROUP BY entity_type
        ) recent_logs ON ss.entity_type = recent_logs.entity_type
      `;

      const params = [];
      if (entityType) {
        query += ' WHERE ss.entity_type = $1';
        params.push(entityType);
      }

      query += ' ORDER BY ss.entity_type';

      const result = await client.query(query, params);

      if (entityType) {
        return result.rows[0] || null;
      }

      return result.rows;

    } finally {
      client.release();
    }
  }

  /**
   * 获取同步日志
   * @param {string} entityType - 实体类型（可选）
   * @param {number} limit - 限制数量
   * @param {number} offset - 偏移量
   * @returns {Promise<Array>} 同步日志
   */
  async getSyncLogs(entityType = null, limit = 100, offset = 0) {
    const client = await pool.connect();

    try {
      let query = `
        SELECT
          entity_type, entity_id, local_id, action, details,
          error_message, processing_time, created_at
        FROM sync_logs
      `;

      const params = [];
      if (entityType) {
        query += ' WHERE entity_type = $1';
        params.push(entityType);
      }

      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
      params.push(limit, offset);

      const result = await client.query(query, params);
      return result.rows;

    } finally {
      client.release();
    }
  }

  /**
   * 清理旧的同步日志
   * @param {number} daysToKeep - 保留天数
   * @returns {Promise<number>} 删除的记录数
   */
  async cleanupOldSyncLogs(daysToKeep = 30) {
    const client = await pool.connect();

    try {
      const result = await client.query(
        'DELETE FROM sync_logs WHERE created_at < NOW() - INTERVAL $1 DAY',
        [daysToKeep]
      );

      const deletedCount = result.rowCount;
      winston.info(`清理旧同步日志: 删除${deletedCount}条记录`);

      // 记录清理操作
      await this.logSyncOperation(
        client,
        'system',
        null,
        null,
        'cleanup',
        { deleted_count: deletedCount, days_kept: daysToKeep }
      );

      return deletedCount;

    } finally {
      client.release();
    }
  }

  /**
   * 暂停同步
   * @param {string} entityType - 实体类型
   */
  async pauseSync(entityType) {
    const client = await pool.connect();

    try {
      await this.updateSyncStatus(client, entityType, {
        status: 'paused'
      });

      winston.info(`同步已暂停: ${entityType}`);

    } finally {
      client.release();
    }
  }

  /**
   * 恢复同步
   * @param {string} entityType - 实体类型
   */
  async resumeSync(entityType) {
    const client = await pool.connect();

    try {
      await this.updateSyncStatus(client, entityType, {
        status: 'idle'
      });

      winston.info(`同步已恢复: ${entityType}`);

    } finally {
      client.release();
    }
  }

  /**
   * 重置同步状态
   * @param {string} entityType - 实体类型
   */
  async resetSyncStatus(entityType) {
    const client = await pool.connect();

    try {
      await this.updateSyncStatus(client, entityType, {
        status: 'idle',
        last_sync_time: null,
        total_synced: 0,
        errors_count: 0,
        last_error_message: null
      });

      winston.info(`同步状态已重置: ${entityType}`);

    } finally {
      client.release();
    }
  }
}

module.exports = SyncEngine;
