const axios = require('axios');
const winston = require('winston');

/**
 * 外网API服务类
 * 基于现有javbusController.js模式实现
 * 负责与外网服务器 http://188.68.60.179:8081 的通信
 */
class ApiService {
  constructor() {
    // 基础配置
    this.baseURL = process.env.EXTERNAL_API_URL || 'http://188.68.60.179:8081';
    this.timeout = parseInt(process.env.API_TIMEOUT || '30000'); // 30秒超时
    this.maxRetries = parseInt(process.env.API_MAX_RETRIES || '3');
    this.retryDelay = parseInt(process.env.API_RETRY_DELAY || '1000'); // 1秒重试延迟
    
    // 创建axios实例
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'User-Agent': 'JAVFLIX-DataCollector/1.0',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    // 设置请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        winston.info(`API请求: ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          timestamp: new Date().toISOString()
        });
        return config;
      },
      (error) => {
        winston.error('API请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );
    
    // 设置响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        winston.info(`API响应成功: ${response.status} ${response.config.url}`, {
          status: response.status,
          dataSize: JSON.stringify(response.data).length,
          timestamp: new Date().toISOString()
        });
        return response;
      },
      (error) => {
        winston.error(`API响应错误: ${error.response?.status || 'NETWORK_ERROR'} ${error.config?.url}`, {
          status: error.response?.status,
          message: error.message,
          timestamp: new Date().toISOString()
        });
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * 通用重试机制
   * @param {Function} apiCall - API调用函数
   * @param {number} retries - 重试次数
   * @returns {Promise} API响应
   */
  async withRetry(apiCall, retries = this.maxRetries) {
    try {
      return await apiCall();
    } catch (error) {
      if (retries > 0 && this.shouldRetry(error)) {
        winston.warn(`API调用失败，${this.retryDelay}ms后重试，剩余重试次数: ${retries}`, {
          error: error.message,
          url: error.config?.url
        });
        
        await this.delay(this.retryDelay);
        return this.withRetry(apiCall, retries - 1);
      }
      throw error;
    }
  }
  
  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否重试
   */
  shouldRetry(error) {
    // 网络错误或5xx服务器错误才重试
    if (!error.response) return true; // 网络错误
    const status = error.response.status;
    return status >= 500 || status === 429; // 服务器错误或限流
  }
  
  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    try {
      const startTime = Date.now();
      const response = await this.client.get('/health', { timeout: 5000 });
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime,
        timestamp: new Date().toISOString(),
        baseURL: this.baseURL
      };
    } catch (error) {
      winston.error('健康检查失败:', error.message);
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString(),
        baseURL: this.baseURL
      };
    }
  }

  /**
   * 通用API调用方法
   * @param {string} endpoint - API端点
   * @param {Object} params - 查询参数
   * @param {string} method - HTTP方法
   * @returns {Promise<Object>} API响应
   */
  async callApi(endpoint, params = {}, method = 'GET') {
    return this.withRetry(async () => {
      const config = { params };
      let response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await this.client.get(endpoint, config);
          break;
        case 'POST':
          response = await this.client.post(endpoint, params);
          break;
        case 'PUT':
          response = await this.client.put(endpoint, params);
          break;
        case 'DELETE':
          response = await this.client.delete(endpoint, config);
          break;
        default:
          throw new Error(`不支持的HTTP方法: ${method}`);
      }

      return response.data;
    });
  }

  /**
   * 探索API端点
   * @param {Array<string>} endpoints - 要测试的端点列表
   * @returns {Promise<Object>} 可用端点信息
   */
  async discoverEndpoints(endpoints = []) {
    const defaultEndpoints = [
      '/api/v1/movies',
      '/api/v1/movies/latest',
      '/api/v1/movies/popular',
      '/api/v1/actors',
      '/api/v1/genres',
      '/api/v1/stats',
      '/movies',
      '/actors',
      '/genres',
      '/download',
      '/magnet',
      '/data'
    ];

    const testEndpoints = endpoints.length > 0 ? endpoints : defaultEndpoints;
    const results = {
      available: [],
      unavailable: [],
      errors: []
    };

    for (const endpoint of testEndpoints) {
      try {
        winston.info(`测试端点: ${endpoint}`);
        const response = await this.client.get(endpoint, { timeout: 5000 });
        results.available.push({
          endpoint,
          status: response.status,
          contentType: response.headers['content-type'],
          dataSize: JSON.stringify(response.data).length
        });
        winston.info(`✅ 端点可用: ${endpoint} (${response.status})`);
      } catch (error) {
        if (error.response) {
          results.unavailable.push({
            endpoint,
            status: error.response.status,
            error: error.response.statusText
          });
          winston.warn(`❌ 端点不可用: ${endpoint} (${error.response.status})`);
        } else {
          results.errors.push({
            endpoint,
            error: error.message
          });
          winston.error(`💥 端点错误: ${endpoint} - ${error.message}`);
        }
      }
    }

    return results;
  }

  /**
   * 获取最新影片列表（灵活端点）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 影片列表
   */
  async getLatestMovies(params = {}) {
    // 尝试多个可能的端点
    const endpoints = ['/api/v1/movies/latest', '/movies/latest', '/api/movies/latest', '/movies'];

    for (const endpoint of endpoints) {
      try {
        return await this.callApi(endpoint, params);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error; // 非404错误直接抛出
        }
        winston.warn(`端点 ${endpoint} 不存在，尝试下一个...`);
      }
    }

    throw new Error('所有影片端点都不可用');
  }

  /**
   * 获取热门影片列表（灵活端点）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 影片列表
   */
  async getPopularMovies(params = {}) {
    const endpoints = ['/api/v1/movies/popular', '/movies/popular', '/api/movies/popular', '/movies'];

    for (const endpoint of endpoints) {
      try {
        return await this.callApi(endpoint, params);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error;
        }
        winston.warn(`端点 ${endpoint} 不存在，尝试下一个...`);
      }
    }

    throw new Error('所有热门影片端点都不可用');
  }

  /**
   * 获取影片详情（灵活端点）
   * @param {string} movieId - 影片ID
   * @returns {Promise<Object>} 影片详情
   */
  async getMovieDetail(movieId) {
    if (!movieId) {
      throw new Error('影片ID不能为空');
    }

    const endpoints = [
      `/api/v1/movies/${movieId}`,
      `/movies/${movieId}`,
      `/api/movies/${movieId}`
    ];

    for (const endpoint of endpoints) {
      try {
        return await this.callApi(endpoint);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error;
        }
        winston.warn(`端点 ${endpoint} 不存在，尝试下一个...`);
      }
    }

    throw new Error(`影片详情端点都不可用: ${movieId}`);
  }

  /**
   * 获取演员列表（灵活端点）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 演员列表
   */
  async getActors(params = {}) {
    const endpoints = ['/api/v1/actors', '/actors', '/api/actors'];

    for (const endpoint of endpoints) {
      try {
        return await this.callApi(endpoint, params);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error;
        }
        winston.warn(`端点 ${endpoint} 不存在，尝试下一个...`);
      }
    }

    throw new Error('所有演员端点都不可用');
  }

  /**
   * 获取分类列表（灵活端点）
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 分类列表
   */
  async getGenres(params = {}) {
    const endpoints = ['/api/v1/genres', '/genres', '/api/genres'];

    for (const endpoint of endpoints) {
      try {
        return await this.callApi(endpoint, params);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error;
        }
        winston.warn(`端点 ${endpoint} 不存在，尝试下一个...`);
      }
    }

    throw new Error('所有分类端点都不可用');
  }

  /**
   * 批量获取影片详情
   * @param {Array<string>} movieIds - 影片ID数组
   * @returns {Promise<Array>} 影片详情数组
   */
  async getBatchMovieDetails(movieIds) {
    if (!Array.isArray(movieIds) || movieIds.length === 0) {
      throw new Error('影片ID数组不能为空');
    }

    return this.withRetry(async () => {
      const response = await this.client.post('/api/v1/movies/batch', { ids: movieIds });
      return response.data;
    });
  }

  /**
   * 获取服务器统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getServerStats() {
    return this.withRetry(async () => {
      const response = await this.client.get('/api/v1/stats');
      return response.data;
    });
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @returns {Object} 格式化的错误信息
   */
  handleError(error) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      message: error.message
    };

    if (error.response) {
      // 服务器响应错误
      errorInfo.status = error.response.status;
      errorInfo.statusText = error.response.statusText;
      errorInfo.data = error.response.data;

      switch (error.response.status) {
        case 401:
          errorInfo.type = 'UNAUTHORIZED';
          errorInfo.description = 'API认证失败，请检查访问权限';
          break;
        case 403:
          errorInfo.type = 'FORBIDDEN';
          errorInfo.description = 'IP白名单限制，请联系管理员添加IP';
          break;
        case 404:
          errorInfo.type = 'NOT_FOUND';
          errorInfo.description = '请求的资源不存在';
          break;
        case 429:
          errorInfo.type = 'RATE_LIMIT';
          errorInfo.description = 'API调用频率超限，请稍后重试';
          break;
        case 500:
          errorInfo.type = 'SERVER_ERROR';
          errorInfo.description = '外网服务器内部错误';
          break;
        default:
          errorInfo.type = 'HTTP_ERROR';
          errorInfo.description = `HTTP错误: ${error.response.status}`;
      }
    } else if (error.request) {
      // 网络错误
      errorInfo.type = 'NETWORK_ERROR';
      errorInfo.description = '网络连接失败，请检查网络连接和服务器状态';
    } else {
      // 其他错误
      errorInfo.type = 'UNKNOWN_ERROR';
      errorInfo.description = '未知错误';
    }

    winston.error('API调用错误:', errorInfo);
    return errorInfo;
  }

  /**
   * 获取API配置信息
   * @returns {Object} 配置信息
   */
  getConfig() {
    return {
      baseURL: this.baseURL,
      timeout: this.timeout,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}

module.exports = ApiService;
