const winston = require('winston');

/**
 * 模拟API服务类
 * 用于开发和测试阶段，提供模拟的JAV数据
 * 当外网API不可用时作为备用方案
 */
class MockApiService {
  constructor() {
    this.baseURL = 'http://188.68.60.179:8081';
    this.isHealthy = true;
    
    winston.info('MockApiService 初始化完成，提供模拟数据服务');
  }
  
  /**
   * 模拟延迟
   * @param {number} ms - 延迟毫秒数
   */
  async delay(ms = 100) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 健康检查
   * @returns {Promise<Object>} 健康状态
   */
  async healthCheck() {
    await this.delay(50);
    return {
      status: 'healthy',
      responseTime: 50,
      timestamp: new Date().toISOString(),
      baseURL: this.baseURL,
      mode: 'mock'
    };
  }
  
  /**
   * 获取最新影片列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 影片列表
   */
  async getLatestMovies(params = {}) {
    await this.delay(200);
    
    const { page = 1, limit = 10 } = params;
    const total = 1000;
    const totalPages = Math.ceil(total / limit);
    
    const movies = [];
    for (let i = 0; i < limit; i++) {
      const id = `MOCK-${String((page - 1) * limit + i + 1).padStart(6, '0')}`;
      movies.push({
        id,
        title: `模拟影片标题 ${id}`,
        release_date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        duration: `${Math.floor(Math.random() * 60) + 60}分钟`,
        image_url: `https://example.com/images/${id}.jpg`,
        description: `这是影片 ${id} 的描述信息`,
        director_id: `DIR-${Math.floor(Math.random() * 100) + 1}`,
        producer_id: `PROD-${Math.floor(Math.random() * 50) + 1}`,
        publisher_id: `PUB-${Math.floor(Math.random() * 20) + 1}`,
        view_count: Math.floor(Math.random() * 10000),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
    
    winston.info(`模拟获取最新影片: 页码${page}, 每页${limit}条, 返回${movies.length}条`);
    
    return {
      success: true,
      data: movies,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 获取热门影片列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 影片列表
   */
  async getPopularMovies(params = {}) {
    await this.delay(200);
    
    const result = await this.getLatestMovies(params);
    // 为热门影片添加更高的观看次数
    result.data.forEach(movie => {
      movie.view_count = Math.floor(Math.random() * 50000) + 10000;
      movie.is_popular = true;
    });
    
    winston.info(`模拟获取热门影片: 返回${result.data.length}条`);
    return result;
  }
  
  /**
   * 获取影片详情
   * @param {string} movieId - 影片ID
   * @returns {Promise<Object>} 影片详情
   */
  async getMovieDetail(movieId) {
    await this.delay(150);
    
    if (!movieId) {
      throw new Error('影片ID不能为空');
    }
    
    const movie = {
      id: movieId,
      title: `模拟影片详情 ${movieId}`,
      release_date: '2024-01-15',
      duration: '120分钟',
      image_url: `https://example.com/images/${movieId}.jpg`,
      cover_image: `https://example.com/covers/${movieId}.jpg`,
      description: `这是影片 ${movieId} 的详细描述信息，包含更多的剧情介绍和背景信息。`,
      director_id: 'DIR-001',
      producer_id: 'PROD-001',
      publisher_id: 'PUB-001',
      series_id: 'SERIES-001',
      view_count: Math.floor(Math.random() * 100000),
      status: 'published',
      genres: ['动作', '剧情', '悬疑'],
      stars: [
        { id: 'STAR-001', name: '演员A' },
        { id: 'STAR-002', name: '演员B' }
      ],
      magnets: [
        {
          id: 'MAG-001',
          link: 'magnet:?xt=urn:btih:example1',
          title: '高清版本',
          size: '2.5GB',
          is_hd: true,
          has_subtitle: true
        }
      ],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    winston.info(`模拟获取影片详情: ${movieId}`);
    
    return {
      success: true,
      data: movie,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 获取演员列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 演员列表
   */
  async getActors(params = {}) {
    await this.delay(180);
    
    const { page = 1, limit = 10 } = params;
    const total = 500;
    const totalPages = Math.ceil(total / limit);
    
    const actors = [];
    for (let i = 0; i < limit; i++) {
      const id = `STAR-${String((page - 1) * limit + i + 1).padStart(4, '0')}`;
      actors.push({
        id,
        name: `演员姓名 ${id}`,
        image_url: `https://example.com/actors/${id}.jpg`,
        birthday: new Date(Date.now() - Math.random() * 30 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        age: Math.floor(Math.random() * 20) + 20,
        height: Math.floor(Math.random() * 30) + 150,
        bust: `${Math.floor(Math.random() * 20) + 80}cm`,
        waist: `${Math.floor(Math.random() * 15) + 55}cm`,
        hip: `${Math.floor(Math.random() * 20) + 80}cm`,
        birthplace: '日本',
        movie_count: Math.floor(Math.random() * 100) + 1,
        debut_date: new Date(Date.now() - Math.random() * 10 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
    
    winston.info(`模拟获取演员列表: 页码${page}, 每页${limit}条, 返回${actors.length}条`);
    
    return {
      success: true,
      data: actors,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 获取分类列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 分类列表
   */
  async getGenres(params = {}) {
    await this.delay(100);
    
    const genres = [
      { id: 'GENRE-001', name: '动作', count: 150 },
      { id: 'GENRE-002', name: '剧情', count: 200 },
      { id: 'GENRE-003', name: '悬疑', count: 80 },
      { id: 'GENRE-004', name: '喜剧', count: 120 },
      { id: 'GENRE-005', name: '爱情', count: 180 },
      { id: 'GENRE-006', name: '科幻', count: 60 },
      { id: 'GENRE-007', name: '恐怖', count: 40 },
      { id: 'GENRE-008', name: '战争', count: 30 },
      { id: 'GENRE-009', name: '历史', count: 25 },
      { id: 'GENRE-010', name: '纪录片', count: 35 }
    ];
    
    winston.info(`模拟获取分类列表: 返回${genres.length}条`);
    
    return {
      success: true,
      data: genres,
      total: genres.length,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 获取服务器统计
   * @returns {Promise<Object>} 统计信息
   */
  async getServerStats() {
    await this.delay(100);
    
    const stats = {
      total_movies: 1000,
      total_actors: 500,
      total_genres: 10,
      total_downloads: 50000,
      server_uptime: '15 days',
      last_update: new Date().toISOString(),
      api_version: '1.0.0-mock'
    };
    
    winston.info('模拟获取服务器统计信息');
    
    return {
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 处理错误（模拟）
   * @param {Error} error - 错误对象
   * @returns {Object} 格式化的错误信息
   */
  handleError(error) {
    return {
      timestamp: new Date().toISOString(),
      message: error.message,
      type: 'MOCK_ERROR',
      description: '模拟API服务错误'
    };
  }
  
  /**
   * 获取配置信息
   * @returns {Object} 配置信息
   */
  getConfig() {
    return {
      baseURL: this.baseURL,
      mode: 'mock',
      timeout: 1000,
      maxRetries: 1,
      retryDelay: 100
    };
  }
}

module.exports = MockApiService;
