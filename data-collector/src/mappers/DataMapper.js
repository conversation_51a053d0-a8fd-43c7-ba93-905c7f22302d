const winston = require('winston');

/**
 * 数据映射器类
 * 基于现有javbusImportController.js的数据处理逻辑
 * 实现外网API数据格式到本地数据库格式的转换映射
 */
class DataMapper {
  constructor() {
    winston.info('DataMapper 初始化完成');
  }
  
  /**
   * 映射影片数据：外网API格式 → movies表格式
   * @param {Object} externalMovie - 外网API影片数据
   * @returns {Object} 本地数据库格式的影片数据
   */
  mapMovieData(externalMovie) {
    try {
      // 基于现有javbusImportController.js的映射逻辑
      const mappedMovie = {
        // 基础字段映射
        movie_id: this.sanitizeString(externalMovie.id || externalMovie.movie_id),
        title: this.sanitizeString(externalMovie.title),
        image_url: this.sanitizeUrl(externalMovie.image_url || externalMovie.imageUrl),
        cover_image: this.sanitizeUrl(externalMovie.cover_image || externalMovie.coverImage),
        cached_image_url: this.sanitizeUrl(externalMovie.cached_image_url || externalMovie.cachedImageUrl),
        release_date: this.parseDate(externalMovie.release_date || externalMovie.releaseDate),
        duration: this.sanitizeString(externalMovie.duration),
        description: this.sanitizeText(externalMovie.description),
        
        // 状态和优先级（基于现有逻辑）
        status: 'published', // 直接设为已发布，跳过视频处理流程
        processing_priority: 5,
        
        // 关联ID字段
        director_id: externalMovie.director_id || externalMovie.directorId,
        producer_id: externalMovie.producer_id || externalMovie.producerId,
        publisher_id: externalMovie.publisher_id || externalMovie.publisherId,
        series_id: externalMovie.series_id || externalMovie.seriesId,
        
        // 时间戳
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      // 处理关联数据
      if (externalMovie.director) {
        mappedMovie.director = this.mapDirectorData(externalMovie.director);
      }
      
      if (externalMovie.producer) {
        mappedMovie.producer = this.mapProducerData(externalMovie.producer);
      }
      
      if (externalMovie.publisher) {
        mappedMovie.publisher = this.mapPublisherData(externalMovie.publisher);
      }
      
      if (externalMovie.series) {
        mappedMovie.series = this.mapSeriesData(externalMovie.series);
      }
      
      if (externalMovie.stars && Array.isArray(externalMovie.stars)) {
        mappedMovie.stars = externalMovie.stars.map(star => this.mapStarData(star));
      }
      
      if (externalMovie.genres && Array.isArray(externalMovie.genres)) {
        mappedMovie.genres = externalMovie.genres.map(genre => this.mapGenreData(genre));
      }
      
      if (externalMovie.magnets && Array.isArray(externalMovie.magnets)) {
        mappedMovie.magnets = externalMovie.magnets.map(magnet => this.mapMagnetData(magnet));
      }
      
      // 判断是否为无码影片（基于现有逻辑）
      mappedMovie.isUncensored = this.determineUncensored(mappedMovie.movie_id);
      
      winston.debug(`影片数据映射完成: ${mappedMovie.movie_id} - ${mappedMovie.title}`);
      return mappedMovie;
      
    } catch (error) {
      winston.error('影片数据映射失败:', error);
      throw new Error(`影片数据映射失败: ${error.message}`);
    }
  }
  
  /**
   * 映射演员数据：外网API格式 → stars表格式
   * @param {Object} externalStar - 外网API演员数据
   * @returns {Object} 本地数据库格式的演员数据
   */
  mapStarData(externalStar) {
    try {
      const mappedStar = {
        star_id: this.sanitizeString(externalStar.id || externalStar.star_id),
        name: this.sanitizeString(externalStar.name),
        image_url: this.sanitizeUrl(externalStar.image_url || externalStar.imageUrl),
        cached_image_url: this.sanitizeUrl(externalStar.cached_image_url || externalStar.cachedImageUrl),
        
        // 个人信息字段
        birthday: this.parseDate(externalStar.birthday),
        age: this.parseInteger(externalStar.age),
        height: this.parseHeight(externalStar.height),
        birthplace: this.sanitizeString(externalStar.birthplace),
        debut_date: this.parseDate(externalStar.debut_date || externalStar.debutDate),
        hobby: this.sanitizeText(externalStar.hobby),
        
        // 身体数据字段
        bust: this.sanitizeString(externalStar.bust),
        waist: this.sanitizeString(externalStar.waist),
        hip: this.sanitizeString(externalStar.hip),
        cup_size: this.sanitizeString(externalStar.cup_size || externalStar.cupSize),
        measurements: this.sanitizeString(externalStar.measurements),
        
        // 其他字段
        description: this.sanitizeText(externalStar.description),
        movie_count: this.parseInteger(externalStar.movie_count || externalStar.movieCount),
        
        // 时间戳
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      winston.debug(`演员数据映射完成: ${mappedStar.star_id} - ${mappedStar.name}`);
      return mappedStar;
      
    } catch (error) {
      winston.error('演员数据映射失败:', error);
      throw new Error(`演员数据映射失败: ${error.message}`);
    }
  }
  
  /**
   * 映射分类数据：外网API格式 → genres表格式
   * @param {Object|string} externalGenre - 外网API分类数据
   * @returns {Object} 本地数据库格式的分类数据
   */
  mapGenreData(externalGenre) {
    try {
      // 如果是字符串，直接作为名称
      if (typeof externalGenre === 'string') {
        return {
          name: this.sanitizeString(externalGenre),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }
      
      // 如果是对象，映射字段
      const mappedGenre = {
        name: this.sanitizeString(externalGenre.name || externalGenre.genre_name),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      winston.debug(`分类数据映射完成: ${mappedGenre.name}`);
      return mappedGenre;
      
    } catch (error) {
      winston.error('分类数据映射失败:', error);
      throw new Error(`分类数据映射失败: ${error.message}`);
    }
  }
  
  /**
   * 映射导演数据
   * @param {Object} externalDirector - 外网API导演数据
   * @returns {Object} 本地数据库格式的导演数据
   */
  mapDirectorData(externalDirector) {
    return {
      director_id: this.sanitizeString(externalDirector.id || externalDirector.director_id),
      name: this.sanitizeString(externalDirector.name),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
  
  /**
   * 映射制作商数据
   * @param {Object} externalProducer - 外网API制作商数据
   * @returns {Object} 本地数据库格式的制作商数据
   */
  mapProducerData(externalProducer) {
    return {
      producer_id: this.sanitizeString(externalProducer.id || externalProducer.producer_id),
      name: this.sanitizeString(externalProducer.name),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
  
  /**
   * 映射发行商数据
   * @param {Object} externalPublisher - 外网API发行商数据
   * @returns {Object} 本地数据库格式的发行商数据
   */
  mapPublisherData(externalPublisher) {
    return {
      publisher_id: this.sanitizeString(externalPublisher.id || externalPublisher.publisher_id),
      name: this.sanitizeString(externalPublisher.name),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
  
  /**
   * 映射系列数据
   * @param {Object} externalSeries - 外网API系列数据
   * @returns {Object} 本地数据库格式的系列数据
   */
  mapSeriesData(externalSeries) {
    return {
      series_id: this.sanitizeString(externalSeries.id || externalSeries.series_id),
      name: this.sanitizeString(externalSeries.name),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
  
  /**
   * 映射磁力链接数据
   * @param {Object} externalMagnet - 外网API磁力链接数据
   * @returns {Object} 本地数据库格式的磁力链接数据
   */
  mapMagnetData(externalMagnet) {
    return {
      magnet_id: this.sanitizeString(externalMagnet.id || externalMagnet.magnet_id),
      link: this.sanitizeString(externalMagnet.link || externalMagnet.magnet_link),
      title: this.sanitizeString(externalMagnet.title),
      size: this.sanitizeString(externalMagnet.size),
      is_hd: Boolean(externalMagnet.is_hd || externalMagnet.isHD),
      has_subtitle: Boolean(externalMagnet.has_subtitle || externalMagnet.hasSubtitle),
      share_date: this.parseDate(externalMagnet.share_date || externalMagnet.shareDate),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // ==================== 数据清洗和工具方法 ====================

  /**
   * 清理字符串数据
   * @param {*} value - 输入值
   * @param {number} maxLength - 最大长度
   * @returns {string|null} 清理后的字符串
   */
  sanitizeString(value, maxLength = 255) {
    if (value === null || value === undefined) return null;

    let str = String(value).trim();
    if (str === '') return null;

    // 移除特殊字符和控制字符
    str = str.replace(/[\x00-\x1F\x7F]/g, '');

    // 限制长度
    if (maxLength && str.length > maxLength) {
      str = str.substring(0, maxLength);
    }

    return str;
  }

  /**
   * 清理文本数据（允许更长的内容）
   * @param {*} value - 输入值
   * @param {number} maxLength - 最大长度
   * @returns {string|null} 清理后的文本
   */
  sanitizeText(value, maxLength = 5000) {
    return this.sanitizeString(value, maxLength);
  }

  /**
   * 清理URL数据
   * @param {*} value - 输入值
   * @returns {string|null} 清理后的URL
   */
  sanitizeUrl(value) {
    if (value === null || value === undefined) return null;

    const url = String(value).trim();
    if (url === '') return null;

    // 基本URL格式验证
    if (!url.match(/^https?:\/\/.+/i) && !url.startsWith('/')) {
      return null;
    }

    return url;
  }

  /**
   * 解析日期
   * @param {*} value - 输入值
   * @returns {string|null} ISO日期字符串或null
   */
  parseDate(value) {
    if (value === null || value === undefined) return null;

    const dateStr = String(value).trim();
    if (dateStr === '') return null;

    // 尝试解析各种日期格式
    const dateFormats = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/, // ISO格式
    ];

    // 标准ISO格式
    if (dateFormats[0].test(dateStr) || dateFormats[2].test(dateStr)) {
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
    }

    // YYYY/MM/DD格式
    if (dateFormats[1].test(dateStr)) {
      const date = new Date(dateStr.replace(/\//g, '-'));
      return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
    }

    // 尝试直接解析
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0];
  }

  /**
   * 解析整数
   * @param {*} value - 输入值
   * @returns {number|null} 整数或null
   */
  parseInteger(value) {
    if (value === null || value === undefined) return null;

    const num = parseInt(value, 10);
    return isNaN(num) ? null : num;
  }

  /**
   * 解析身高（处理cm单位）
   * @param {*} value - 输入值
   * @returns {number|null} 身高数值或null
   */
  parseHeight(value) {
    if (value === null || value === undefined) return null;

    const heightStr = String(value).trim();
    if (heightStr === '') return null;

    // 移除cm单位
    const heightNum = parseInt(heightStr.replace(/cm/i, ''), 10);

    // 验证合理范围（100-250cm）
    if (isNaN(heightNum) || heightNum < 100 || heightNum > 250) {
      return null;
    }

    return heightNum;
  }

  /**
   * 判断是否为无码影片（基于现有javbusImportController.js逻辑）
   * @param {string} movieId - 影片ID
   * @returns {boolean} 是否为无码
   */
  determineUncensored(movieId) {
    if (!movieId) return false;

    // 常见无码ID格式: n1234, heyzo-1234, xxx-av-12345, k1234, 1pondo-123456_789 等
    const uncensoredPattern = /^(n\d+|heyzo-?\d+|xxx-av-?\d+|k\d+|\d{6}[-_]\d{3}|carib|pondo|gachi|1pon|mura|siro|fc2)/i;
    return uncensoredPattern.test(movieId);
  }

  /**
   * 批量映射影片数据
   * @param {Array} externalMovies - 外网API影片数据数组
   * @returns {Array} 映射后的影片数据数组
   */
  mapMoviesData(externalMovies) {
    if (!Array.isArray(externalMovies)) {
      throw new Error('输入必须是数组');
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < externalMovies.length; i++) {
      try {
        const mappedMovie = this.mapMovieData(externalMovies[i]);
        results.push(mappedMovie);
      } catch (error) {
        winston.error(`映射第${i + 1}部影片失败:`, error);
        errors.push({
          index: i,
          data: externalMovies[i],
          error: error.message
        });
      }
    }

    winston.info(`批量映射完成: 成功${results.length}部，失败${errors.length}部`);

    return {
      success: results,
      errors: errors,
      total: externalMovies.length,
      successCount: results.length,
      errorCount: errors.length
    };
  }

  /**
   * 批量映射演员数据
   * @param {Array} externalStars - 外网API演员数据数组
   * @returns {Array} 映射后的演员数据数组
   */
  mapStarsData(externalStars) {
    if (!Array.isArray(externalStars)) {
      throw new Error('输入必须是数组');
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < externalStars.length; i++) {
      try {
        const mappedStar = this.mapStarData(externalStars[i]);
        results.push(mappedStar);
      } catch (error) {
        winston.error(`映射第${i + 1}位演员失败:`, error);
        errors.push({
          index: i,
          data: externalStars[i],
          error: error.message
        });
      }
    }

    winston.info(`批量演员映射完成: 成功${results.length}位，失败${errors.length}位`);

    return {
      success: results,
      errors: errors,
      total: externalStars.length,
      successCount: results.length,
      errorCount: errors.length
    };
  }
}

module.exports = DataMapper;
