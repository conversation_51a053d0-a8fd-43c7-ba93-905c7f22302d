
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.12.0
 * Query Engine version: 8047c96bbd92db98a2abc7c9323ce77c02c89dbc
 */
Prisma.prismaVersion = {
  client: "6.12.0",
  engine: "8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CategoriesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  image_url: 'image_url',
  count: 'count',
  description: 'description',
  color: 'color',
  icon: 'icon',
  is_featured: 'is_featured',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.DirectorsScalarFieldEnum = {
  id: 'id',
  director_id: 'director_id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.GenresScalarFieldEnum = {
  id: 'id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.MagnetsScalarFieldEnum = {
  id: 'id',
  movie_id: 'movie_id',
  magnet_id: 'magnet_id',
  link: 'link',
  title: 'title',
  size: 'size',
  is_hd: 'is_hd',
  has_subtitle: 'has_subtitle',
  share_date: 'share_date',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Movie_directorsScalarFieldEnum = {
  movie_id: 'movie_id',
  director_id: 'director_id'
};

exports.Prisma.Movie_genresScalarFieldEnum = {
  movie_id: 'movie_id',
  genre_id: 'genre_id'
};

exports.Prisma.Movie_starsScalarFieldEnum = {
  movie_id: 'movie_id',
  star_id: 'star_id'
};

exports.Prisma.MoviesScalarFieldEnum = {
  id: 'id',
  movie_id: 'movie_id',
  title: 'title',
  image_url: 'image_url',
  cover_image: 'cover_image',
  release_date: 'release_date',
  duration: 'duration',
  description: 'description',
  director_id: 'director_id',
  producer_id: 'producer_id',
  publisher_id: 'publisher_id',
  series_id: 'series_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  cached_image_url: 'cached_image_url',
  view_count: 'view_count',
  status: 'status',
  video_urls: 'video_urls',
  processing_priority: 'processing_priority',
  published_at: 'published_at',
  processing_started_at: 'processing_started_at',
  processing_completed_at: 'processing_completed_at'
};

exports.Prisma.ProducersScalarFieldEnum = {
  id: 'id',
  producer_id: 'producer_id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PublishersScalarFieldEnum = {
  id: 'id',
  publisher_id: 'publisher_id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SamplesScalarFieldEnum = {
  id: 'id',
  movie_id: 'movie_id',
  sample_id: 'sample_id',
  alt: 'alt',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SeriesScalarFieldEnum = {
  id: 'id',
  series_id: 'series_id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Similar_moviesScalarFieldEnum = {
  id: 'id',
  movie_id: 'movie_id',
  similar_movie_id: 'similar_movie_id',
  similar_title: 'similar_title',
  similar_image: 'similar_image',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.StarsScalarFieldEnum = {
  id: 'id',
  star_id: 'star_id',
  name: 'name',
  image_url: 'image_url',
  created_at: 'created_at',
  updated_at: 'updated_at',
  cached_image_url: 'cached_image_url',
  birthday: 'birthday',
  age: 'age',
  height: 'height',
  bust: 'bust',
  waist: 'waist',
  hip: 'hip',
  birthplace: 'birthplace',
  hobby: 'hobby',
  waistline: 'waistline',
  hipline: 'hipline',
  cup_size: 'cup_size',
  movie_count: 'movie_count',
  debut_date: 'debut_date',
  measurements: 'measurements',
  description: 'description',
  javbus_id: 'javbus_id',
  bust_size: 'bust_size',
  waist_size: 'waist_size',
  hip_size: 'hip_size'
};

exports.Prisma.UsersScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  is_admin: 'is_admin',
  created_at: 'created_at',
  lastLoginAt: 'lastLoginAt',
  updated_at: 'updated_at',
  updatedAt: 'updatedAt',
  createdAt: 'createdAt',
  is_active: 'is_active'
};

exports.Prisma.Video_categoriesScalarFieldEnum = {
  video_id: 'video_id',
  category_id: 'category_id'
};

exports.Prisma.User_favoritesScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  video_id: 'video_id',
  note: 'note',
  created_at: 'created_at'
};

exports.Prisma.User_followingScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  star_id: 'star_id',
  created_at: 'created_at'
};

exports.Prisma.User_likesScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  video_id: 'video_id',
  created_at: 'created_at'
};

exports.Prisma.Watch_historyScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  video_id: 'video_id',
  progress: 'progress',
  completed: 'completed',
  watched_at: 'watched_at'
};

exports.Prisma.Video_statsScalarFieldEnum = {
  id: 'id',
  views: 'views',
  likes: 'likes',
  favorites: 'favorites',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Video_processing_tasksScalarFieldEnum = {
  id: 'id',
  task_uuid: 'task_uuid',
  movie_id: 'movie_id',
  status: 'status',
  priority: 'priority',
  config: 'config',
  progress: 'progress',
  error_message: 'error_message',
  started_at: 'started_at',
  completed_at: 'completed_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Movie_status_logsScalarFieldEnum = {
  id: 'id',
  movie_id: 'movie_id',
  old_status: 'old_status',
  new_status: 'new_status',
  changed_by: 'changed_by',
  reason: 'reason',
  created_at: 'created_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  categories: 'categories',
  directors: 'directors',
  genres: 'genres',
  magnets: 'magnets',
  movie_directors: 'movie_directors',
  movie_genres: 'movie_genres',
  movie_stars: 'movie_stars',
  movies: 'movies',
  producers: 'producers',
  publishers: 'publishers',
  samples: 'samples',
  series: 'series',
  similar_movies: 'similar_movies',
  stars: 'stars',
  users: 'users',
  video_categories: 'video_categories',
  user_favorites: 'user_favorites',
  user_following: 'user_following',
  user_likes: 'user_likes',
  watch_history: 'watch_history',
  video_stats: 'video_stats',
  video_processing_tasks: 'video_processing_tasks',
  movie_status_logs: 'movie_status_logs'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
